#!/usr/bin/env python3
"""
数智体系统测试脚本
System test script for Data Intelligence Agent
"""
import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path

# 添加app目录到Python路径
sys.path.append('app')

def create_test_data():
    """创建测试数据"""
    print("📊 创建测试数据...")
    
    # 创建示例CSV数据
    np.random.seed(42)
    data = {
        '日期': pd.date_range('2023-01-01', periods=100, freq='D'),
        '销售额': np.random.normal(10000, 2000, 100),
        '客户数量': np.random.poisson(50, 100),
        '产品类别': np.random.choice(['A', 'B', 'C'], 100),
        '地区': np.random.choice(['北京', '上海', '广州', '深圳'], 100),
        '满意度评分': np.random.uniform(3.0, 5.0, 100)
    }
    
    df = pd.DataFrame(data)
    
    # 添加一些缺失值和异常值
    df.loc[5:10, '销售额'] = np.nan
    df.loc[20, '销售额'] = 50000  # 异常值
    df.loc[30:35, '满意度评分'] = np.nan
    
    # 保存测试数据
    os.makedirs('test_data', exist_ok=True)
    df.to_csv('test_data/sample_sales_data.csv', index=False, encoding='utf-8-sig')
    
    print("✅ 测试数据已创建: test_data/sample_sales_data.csv")
    return df

def test_file_handler():
    """测试文件处理器"""
    print("\n🔧 测试文件处理器...")
    
    try:
        from app.utils.file_handler import FileHandler
        from app.models.data_models import FileType
        
        # 读取测试文件
        with open('test_data/sample_sales_data.csv', 'rb') as f:
            file_content = f.read()
        
        # 处理文件
        processed_file = FileHandler.process_uploaded_file(file_content, 'sample_sales_data.csv')
        
        # 验证结果
        assert processed_file.type == FileType.CSV
        assert processed_file.dataframe is not None
        assert len(processed_file.dataframe) == 100
        
        print("✅ 文件处理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 文件处理器测试失败: {e}")
        return False

def test_ai_models():
    """测试AI模型管理器"""
    print("\n🧠 测试AI模型管理器...")
    
    try:
        from app.models.ai_models import AIModelManager
        
        manager = AIModelManager()
        available_models = manager.get_available_models()
        
        print(f"可用模型: {list(available_models.keys())}")
        
        if available_models:
            print("✅ AI模型管理器测试通过")
            return True
        else:
            print("⚠️  没有可用的AI模型，请检查配置")
            return False
            
    except Exception as e:
        print(f"❌ AI模型管理器测试失败: {e}")
        return False

def test_data_analysis():
    """测试数据分析功能"""
    print("\n📈 测试数据分析功能...")
    
    try:
        from app.utils.helpers import DataAnalysisHelper
        
        # 读取测试数据
        df = pd.read_csv('test_data/sample_sales_data.csv')
        
        # 生成数据摘要
        summary = DataAnalysisHelper.generate_data_summary(df)
        
        # 验证摘要内容
        assert '基本信息' in summary
        assert '列信息' in summary
        assert summary['基本信息']['行数'] == 100
        
        # 创建可视化
        visualizations = DataAnalysisHelper.create_basic_visualizations(df)
        assert len(visualizations) > 0
        
        print("✅ 数据分析功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据分析功能测试失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n⚙️ 测试配置...")
    
    try:
        from app.config import Config
        
        # 测试配置加载
        assert Config.APP_NAME == "数智体"
        assert Config.APP_VERSION == "1.0.0"
        
        # 测试目录创建
        Config.ensure_directories()
        assert os.path.exists(Config.OUTPUT_DIR)
        assert os.path.exists(Config.TEMP_DIR)
        
        print("✅ 配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_services():
    """测试服务层"""
    print("\n🔧 测试服务层...")
    
    try:
        # 测试导入
        from app.services.data_cleaner import DataCleaningService
        from app.services.data_analyzer import DataAnalysisService
        from app.services.result_parser import ResultParsingService
        from app.services.article_writer import ArticleWritingService
        
        print("✅ 服务层导入测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 服务层测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🧪 数智体系统测试")
    print("=" * 50)
    
    tests = [
        ("创建测试数据", create_test_data),
        ("配置测试", test_config),
        ("文件处理器测试", test_file_handler),
        ("AI模型管理器测试", test_ai_models),
        ("数据分析功能测试", test_data_analysis),
        ("服务层测试", test_services)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_name == "创建测试数据":
                test_func()  # 这个函数不返回布尔值
                passed += 1
            else:
                if test_func():
                    passed += 1
        except Exception as e:
            print(f"❌ {test_name} 执行失败: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        print("\n🚀 启动应用:")
        print("python run.py")
        print("或者:")
        print("streamlit run app/main.py")
    else:
        print("⚠️  部分测试失败，请检查配置和依赖。")
    
    return passed == total

if __name__ == "__main__":
    run_all_tests()
