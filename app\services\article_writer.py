"""
文章撰写服务
Article Writing Service for Data Intelligence Agent
"""
import json
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from typing import Dict, Any, List, Optional
from app.models.data_models import UploadedFile, ProcessingResult, TaskConfig
from app.models.ai_models import BaseAIModel
from app.utils.helpers import <PERSON><PERSON><PERSON><PERSON><PERSON>, TimeHelper, DataAnalysisHelper
from app.utils.file_handler import FileHandler

class ArticleWritingService:
    """文章撰写服务"""
    
    def __init__(self, ai_model: BaseAIModel):
        self.ai_model = ai_model
    
    def write_article(self, uploaded_file: UploadedFile, task_config: TaskConfig) -> ProcessingResult:
        """撰写文章"""
        start_time = TimeHelper.get_readable_time()
        
        try:
            # 分析上传的数据
            data_analysis = self._analyze_uploaded_data(uploaded_file)
            
            # 创建AI提示词
            prompt = PromptHelper.create_article_writing_prompt(
                data_analysis, task_config.prompt
            )
            
            # 获取AI生成的文章
            ai_article = self.ai_model.generate_response(prompt, max_tokens=4000)
            
            # 结构化文章内容
            structured_article = self._structure_article(ai_article, data_analysis)
            
            # 创建Word文档
            doc = self._create_article_document(structured_article, data_analysis)
            
            # 生成纯文本版本
            text_version = self._create_text_version(structured_article)
            
            # 保存结果
            output_files = []
            if task_config.output_path:
                # 保存Word文档
                doc_path = f"{task_config.output_path}/article_{TimeHelper.get_timestamp()}.docx"
                if self._save_word_document(doc, doc_path):
                    output_files.append(doc_path)
                
                # 保存文本版本
                text_path = f"{task_config.output_path}/article_{TimeHelper.get_timestamp()}.txt"
                if FileHandler.save_text(text_version, text_path):
                    output_files.append(text_path)
                
                # 保存结构化数据
                structure_path = f"{task_config.output_path}/article_structure_{TimeHelper.get_timestamp()}.json"
                if FileHandler.save_text(json.dumps(structured_article, indent=2, ensure_ascii=False), structure_path):
                    output_files.append(structure_path)
            
            return ProcessingResult(
                success=True,
                result_data={
                    "structured_article": structured_article,
                    "word_document": doc,
                    "text_version": text_version,
                    "data_analysis": data_analysis,
                    "ai_article": ai_article
                },
                output_files=output_files,
                metadata={
                    "processing_time": start_time,
                    "article_length": len(text_version),
                    "sections": len(structured_article.get("sections", []))
                }
            )
            
        except Exception as e:
            return ProcessingResult(
                success=False,
                error_message=f"文章撰写失败: {str(e)}"
            )
    
    def _analyze_uploaded_data(self, uploaded_file: UploadedFile) -> Dict[str, Any]:
        """分析上传的数据"""
        analysis = {
            "文件信息": {
                "文件名": uploaded_file.name,
                "文件类型": uploaded_file.type.value,
                "文件大小": f"{uploaded_file.size / 1024:.2f} KB"
            },
            "数据概览": {},
            "内容摘要": ""
        }
        
        # 如果是数据表格
        if uploaded_file.dataframe is not None:
            df = uploaded_file.dataframe
            analysis["数据概览"] = DataAnalysisHelper.generate_data_summary(df)
            analysis["内容摘要"] = f"数据集包含{len(df)}行{len(df.columns)}列，主要字段包括：{', '.join(df.columns[:5])}"
        
        # 如果是文本内容
        elif uploaded_file.text_content:
            text = uploaded_file.text_content
            analysis["内容摘要"] = text[:500] + "..." if len(text) > 500 else text
            analysis["数据概览"] = {
                "文本长度": len(text),
                "行数": len(text.split('\n')),
                "字符统计": {
                    "中文字符": sum(1 for char in text if '\u4e00' <= char <= '\u9fff'),
                    "英文字符": sum(1 for char in text if char.isalpha() and char.isascii()),
                    "数字字符": sum(1 for char in text if char.isdigit())
                }
            }
        
        # 添加元数据
        if uploaded_file.metadata:
            analysis["元数据"] = uploaded_file.metadata
        
        return analysis
    
    def _structure_article(self, ai_article: str, data_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """结构化文章内容"""
        # 尝试从AI生成的文章中提取结构
        sections = self._extract_sections(ai_article)
        
        if not sections:
            # 如果无法提取结构，创建默认结构
            sections = self._create_default_structure(ai_article, data_analysis)
        
        structured = {
            "标题": self._extract_title(ai_article),
            "摘要": self._extract_abstract(ai_article),
            "关键词": self._extract_keywords(ai_article),
            "sections": sections,
            "参考文献": self._extract_references(ai_article),
            "附录": {
                "数据来源": data_analysis.get("文件信息", {}),
                "分析时间": TimeHelper.get_readable_time()
            }
        }
        
        return structured
    
    def _extract_sections(self, text: str) -> List[Dict[str, str]]:
        """从文本中提取章节"""
        sections = []
        lines = text.split('\n')
        current_section = None
        current_content = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否是标题行（包含数字编号或特定关键词）
            if (line.startswith(('1.', '2.', '3.', '4.', '5.', '一、', '二、', '三、', '四、', '五、')) or
                any(keyword in line for keyword in ['引言', '背景', '方法', '结果', '讨论', '结论', '参考文献'])):
                
                # 保存前一个章节
                if current_section and current_content:
                    sections.append({
                        "title": current_section,
                        "content": '\n'.join(current_content)
                    })
                
                # 开始新章节
                current_section = line
                current_content = []
            else:
                if current_section:
                    current_content.append(line)
        
        # 保存最后一个章节
        if current_section and current_content:
            sections.append({
                "title": current_section,
                "content": '\n'.join(current_content)
            })
        
        return sections
    
    def _create_default_structure(self, text: str, data_analysis: Dict[str, Any]) -> List[Dict[str, str]]:
        """创建默认文章结构"""
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        
        sections = [
            {
                "title": "1. 引言",
                "content": paragraphs[0] if paragraphs else "本文基于提供的数据进行分析和讨论。"
            },
            {
                "title": "2. 数据来源与方法",
                "content": f"数据来源：{data_analysis.get('文件信息', {}).get('文件名', '未知')}\n" +
                          f"数据概览：{data_analysis.get('内容摘要', '')}"
            },
            {
                "title": "3. 分析结果",
                "content": '\n\n'.join(paragraphs[1:-1]) if len(paragraphs) > 2 else paragraphs[1] if len(paragraphs) > 1 else ""
            },
            {
                "title": "4. 结论与建议",
                "content": paragraphs[-1] if paragraphs else "基于以上分析，我们得出了相关结论和建议。"
            }
        ]
        
        return sections
    
    def _extract_title(self, text: str) -> str:
        """提取标题"""
        lines = text.split('\n')
        for line in lines[:5]:  # 在前5行中寻找标题
            line = line.strip()
            if line and not line.startswith(('1.', '一、', '摘要', '关键词')):
                return line
        return "数据分析报告"
    
    def _extract_abstract(self, text: str) -> str:
        """提取摘要"""
        lines = text.split('\n')
        abstract_lines = []
        in_abstract = False
        
        for line in lines:
            line = line.strip()
            if '摘要' in line or 'abstract' in line.lower():
                in_abstract = True
                continue
            elif in_abstract and ('关键词' in line or 'keywords' in line.lower() or line.startswith('1.')):
                break
            elif in_abstract and line:
                abstract_lines.append(line)
        
        if abstract_lines:
            return '\n'.join(abstract_lines)
        else:
            # 如果没有找到摘要，使用前两句话
            sentences = text.split('。')[:2]
            return '。'.join(sentences) + '。'
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        lines = text.split('\n')
        for line in lines:
            if '关键词' in line or 'keywords' in line.lower():
                # 提取关键词
                keywords_text = line.split('：')[-1] if '：' in line else line.split(':')[-1]
                keywords = [kw.strip() for kw in keywords_text.split('，') if kw.strip()]
                if not keywords:
                    keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]
                return keywords[:5]  # 最多5个关键词
        
        # 如果没有找到关键词，生成默认关键词
        return ["数据分析", "智能分析", "数据挖掘", "统计分析"]
    
    def _extract_references(self, text: str) -> List[str]:
        """提取参考文献"""
        lines = text.split('\n')
        references = []
        in_references = False
        
        for line in lines:
            line = line.strip()
            if '参考文献' in line or 'references' in line.lower():
                in_references = True
                continue
            elif in_references and line:
                if line.startswith('[') or line.startswith('1.') or line.startswith('•'):
                    references.append(line)
        
        return references
    
    def _create_article_document(self, structured_article: Dict[str, Any], data_analysis: Dict[str, Any]) -> Document:
        """创建文章Word文档"""
        doc = Document()
        
        # 设置文档标题
        title = doc.add_heading(structured_article.get("标题", "数据分析报告"), 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加作者和日期
        author_para = doc.add_paragraph()
        author_para.add_run("作者：数智体 (Data Intelligence Agent)")
        author_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        date_para = doc.add_paragraph()
        date_para.add_run(f"日期：{TimeHelper.get_readable_time()}")
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        doc.add_paragraph("")  # 空行
        
        # 添加摘要
        doc.add_heading("摘要", level=1)
        doc.add_paragraph(structured_article.get("摘要", ""))
        
        # 添加关键词
        keywords = structured_article.get("关键词", [])
        if keywords:
            doc.add_paragraph(f"关键词：{', '.join(keywords)}")
        
        doc.add_paragraph("")  # 空行
        
        # 添加各个章节
        sections = structured_article.get("sections", [])
        for section in sections:
            doc.add_heading(section["title"], level=1)
            
            # 分段添加内容
            content_paragraphs = section["content"].split('\n\n')
            for para in content_paragraphs:
                if para.strip():
                    doc.add_paragraph(para.strip())
        
        # 添加参考文献
        references = structured_article.get("参考文献", [])
        if references:
            doc.add_heading("参考文献", level=1)
            for ref in references:
                doc.add_paragraph(ref)
        
        # 添加附录
        doc.add_heading("附录", level=1)
        appendix = structured_article.get("附录", {})
        for key, value in appendix.items():
            doc.add_paragraph(f"{key}：{value}")
        
        return doc
    
    def _create_text_version(self, structured_article: Dict[str, Any]) -> str:
        """创建纯文本版本"""
        text_parts = []
        
        # 标题
        text_parts.append(structured_article.get("标题", "数据分析报告"))
        text_parts.append("=" * 50)
        text_parts.append("")
        
        # 基本信息
        text_parts.append(f"作者：数智体 (Data Intelligence Agent)")
        text_parts.append(f"日期：{TimeHelper.get_readable_time()}")
        text_parts.append("")
        
        # 摘要
        text_parts.append("摘要")
        text_parts.append("-" * 20)
        text_parts.append(structured_article.get("摘要", ""))
        text_parts.append("")
        
        # 关键词
        keywords = structured_article.get("关键词", [])
        if keywords:
            text_parts.append(f"关键词：{', '.join(keywords)}")
            text_parts.append("")
        
        # 各个章节
        sections = structured_article.get("sections", [])
        for section in sections:
            text_parts.append(section["title"])
            text_parts.append("-" * 30)
            text_parts.append(section["content"])
            text_parts.append("")
        
        # 参考文献
        references = structured_article.get("参考文献", [])
        if references:
            text_parts.append("参考文献")
            text_parts.append("-" * 20)
            for ref in references:
                text_parts.append(ref)
            text_parts.append("")
        
        return '\n'.join(text_parts)
    
    def _save_word_document(self, doc: Document, filepath: str) -> bool:
        """保存Word文档"""
        try:
            import os
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            doc.save(filepath)
            return True
        except Exception as e:
            print(f"保存Word文档失败: {str(e)}")
            return False
