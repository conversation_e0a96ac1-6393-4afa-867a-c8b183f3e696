#!/usr/bin/env python3
"""
测试广州数据筛选功能
Test Guangzhou data filtering functionality
"""
import sys
import os
import pandas as pd

# 添加app目录到路径
sys.path.append('app')

def test_guangzhou_filter():
    """测试广州数据筛选"""
    print("🧪 测试广州数据筛选功能")
    print("=" * 50)
    
    # 读取测试数据
    try:
        df = pd.read_csv('test_data/sample_sales_data.csv')
        print(f"✅ 成功读取测试数据: {len(df)} 行")
    except Exception as e:
        print(f"❌ 读取测试数据失败: {e}")
        return
    
    # 显示数据概览
    print(f"\n📊 数据概览:")
    print(f"- 总行数: {len(df)}")
    print(f"- 列数: {len(df.columns)}")
    print(f"- 列名: {list(df.columns)}")
    
    # 检查地区列
    if '地区' in df.columns:
        print(f"\n🌍 地区分布:")
        region_counts = df['地区'].value_counts()
        for region, count in region_counts.items():
            print(f"- {region}: {count} 条记录")
    else:
        print("❌ 未找到'地区'列")
        return
    
    # 筛选广州数据
    print(f"\n🔍 筛选广州数据...")
    guangzhou_data = df[df['地区'] == '广州']
    print(f"✅ 筛选结果: {len(guangzhou_data)} 条广州地区的记录")
    
    # 显示广州数据详情
    if len(guangzhou_data) > 0:
        print(f"\n📋 广州地区数据详情:")
        print(guangzhou_data.to_string(index=False))
        
        # 统计分析
        print(f"\n📈 广州地区数据统计:")
        if '销售额' in guangzhou_data.columns:
            sales_data = guangzhou_data['销售额'].dropna()
            if len(sales_data) > 0:
                print(f"- 销售额记录数: {len(sales_data)}")
                print(f"- 平均销售额: {sales_data.mean():.2f}")
                print(f"- 最高销售额: {sales_data.max():.2f}")
                print(f"- 最低销售额: {sales_data.min():.2f}")
        
        if '客户数量' in guangzhou_data.columns:
            customer_data = guangzhou_data['客户数量'].dropna()
            if len(customer_data) > 0:
                print(f"- 平均客户数量: {customer_data.mean():.1f}")
        
        if '产品类别' in guangzhou_data.columns:
            product_counts = guangzhou_data['产品类别'].value_counts()
            print(f"- 产品类别分布:")
            for product, count in product_counts.items():
                print(f"  * {product}: {count} 次")
    
    # 测试AI模型调用
    print(f"\n🤖 测试AI模型调用...")
    try:
        from app.models.ai_models import OllamaModel
        
        model = OllamaModel()
        prompt = "请帮我提取地区为广州的数据，并分析其特征"
        
        print("发送提示词:", prompt)
        response = model.generate_response(prompt)
        print("AI响应:")
        print(response)
        
    except Exception as e:
        print(f"❌ AI模型调用失败: {e}")
    
    # 测试数据清洗服务
    print(f"\n🧹 测试数据清洗服务...")
    try:
        from app.models.data_models import UploadedFile, TaskConfig, TaskType, FileType
        from app.services.data_cleaner import DataCleaningService
        from app.models.ai_models import OllamaModel
        
        # 创建模拟的上传文件对象
        uploaded_file = UploadedFile(
            name="sample_sales_data.csv",
            size=len(df) * 100,  # 估算大小
            type=FileType.CSV,
            dataframe=df
        )
        
        # 创建任务配置
        task_config = TaskConfig(
            task_type=TaskType.DATA_CLEANING,
            prompt="请帮我提取地区为广州的数据",
            model_provider="ollama",
            model_name="llama2",
            output_path="outputs"
        )
        
        # 创建AI模型和清洗服务
        ai_model = OllamaModel()
        cleaner = DataCleaningService(ai_model)
        
        # 执行清洗
        result = cleaner.clean_data(uploaded_file, task_config)
        
        if result.success:
            print("✅ 数据清洗成功!")
            cleaned_df = result.result_data.get("cleaned_dataframe")
            if cleaned_df is not None:
                print(f"清洗后数据行数: {len(cleaned_df)}")
                if len(cleaned_df) > 0:
                    print("清洗后的数据预览:")
                    print(cleaned_df.head().to_string(index=False))
        else:
            print(f"❌ 数据清洗失败: {result.error_message}")
            
    except Exception as e:
        print(f"❌ 数据清洗服务测试失败: {e}")
    
    print(f"\n🎉 测试完成!")

if __name__ == "__main__":
    test_guangzhou_filter()
