#!/usr/bin/env python3
"""
数智体简化版启动脚本
Simple startup script for Data Intelligence Agent (Simplified Version)
"""
import os
import sys
import subprocess

def main():
    """主函数"""
    print("🤖 数智体 (Data Intelligence Agent) - 简化版")
    print("=" * 60)
    
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print(f"📁 工作目录: {script_dir}")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查必要的包
    required_packages = ['streamlit', 'pandas', 'plotly', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n📦 正在安装缺失的包: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install"
            ] + missing_packages)
            print("✅ 所有包安装完成")
        except:
            print("❌ 包安装失败，请手动安装:")
            print(f"pip install {' '.join(missing_packages)}")
            return
    
    # 检查简化版主文件是否存在
    if not os.path.exists("main_simple.py"):
        print("❌ 找不到main_simple.py文件")
        return
    
    # 启动应用
    print("\n🚀 正在启动数智体简化版...")
    print("📍 应用将在浏览器中打开")
    print("💡 按 Ctrl+C 停止应用")
    print("🌐 访问地址: http://localhost:8501\n")
    
    try:
        # 使用streamlit run命令启动应用
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "main_simple.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
