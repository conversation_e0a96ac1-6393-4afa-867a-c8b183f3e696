"""
数据分析服务
Data Analysis Service for Data Intelligence Agent
"""
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
from typing import Dict, Any, List, Optional, Tuple
from app.models.data_models import UploadedFile, ProcessingResult, TaskConfig, AnalysisReport
from app.models.ai_models import BaseAIModel
from app.utils.helpers import <PERSON><PERSON><PERSON>el<PERSON>, DataAnalysisHelper, TimeHelper
from app.utils.file_handler import FileHandler

class DataAnalysisService:
    """数据分析服务"""
    
    def __init__(self, ai_model: BaseAIModel):
        self.ai_model = ai_model
    
    def analyze_data(self, uploaded_file: UploadedFile, task_config: TaskConfig) -> ProcessingResult:
        """执行数据分析"""
        start_time = TimeHelper.get_readable_time()
        
        try:
            # 检查是否有可分析的数据
            if uploaded_file.dataframe is None:
                return ProcessingResult(
                    success=False,
                    error_message="无法从上传的文件中提取可分析的数据"
                )
            
            df = uploaded_file.dataframe.copy()
            
            # 生成数据摘要
            data_summary = DataAnalysisHelper.generate_data_summary(df)
            
            # 创建AI提示词
            prompt = PromptHelper.create_data_analysis_prompt(
                data_summary, task_config.prompt
            )
            
            # 获取AI分析
            ai_analysis = self.ai_model.generate_response(prompt)
            
            # 执行统计分析
            statistical_analysis = self._perform_statistical_analysis(df)
            
            # 生成可视化
            visualizations = self._create_visualizations(df, task_config.prompt)
            
            # 执行高级分析
            advanced_analysis = self._perform_advanced_analysis(df, task_config.prompt)
            
            # 生成分析报告
            analysis_report = self._generate_analysis_report(
                df, statistical_analysis, advanced_analysis, ai_analysis, visualizations
            )
            
            # 保存结果
            output_files = []
            if task_config.output_path:
                # 保存分析报告
                report_path = f"{task_config.output_path}/analysis_report_{TimeHelper.get_timestamp()}.txt"
                if FileHandler.save_text(analysis_report.detailed_analysis, report_path):
                    output_files.append(report_path)
                
                # 保存统计结果
                stats_path = f"{task_config.output_path}/statistical_analysis_{TimeHelper.get_timestamp()}.json"
                if FileHandler.save_text(json.dumps(statistical_analysis, indent=2, ensure_ascii=False), stats_path):
                    output_files.append(stats_path)
                
                # 保存可视化图表
                for i, viz in enumerate(visualizations):
                    if viz.get("figure"):
                        chart_path = f"{task_config.output_path}/chart_{i+1}_{TimeHelper.get_timestamp()}.html"
                        viz["figure"].write_html(chart_path)
                        output_files.append(chart_path)
            
            return ProcessingResult(
                success=True,
                result_data={
                    "analysis_report": analysis_report,
                    "statistical_analysis": statistical_analysis,
                    "advanced_analysis": advanced_analysis,
                    "visualizations": visualizations,
                    "ai_analysis": ai_analysis,
                    "data_summary": data_summary
                },
                output_files=output_files,
                metadata={
                    "processing_time": start_time,
                    "data_shape": df.shape
                }
            )
            
        except Exception as e:
            return ProcessingResult(
                success=False,
                error_message=f"数据分析失败: {str(e)}"
            )
    
    def _perform_statistical_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """执行统计分析"""
        analysis = {
            "基础统计": {},
            "数据分布": {},
            "相关性分析": {},
            "异常值检测": {}
        }
        
        # 基础统计
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            analysis["基础统计"] = df[numeric_cols].describe().to_dict()
        
        # 数据分布分析
        for col in numeric_cols:
            analysis["数据分布"][col] = {
                "偏度": float(df[col].skew()),
                "峰度": float(df[col].kurtosis()),
                "正态性检验": self._test_normality(df[col])
            }
        
        # 相关性分析
        if len(numeric_cols) > 1:
            corr_matrix = df[numeric_cols].corr()
            analysis["相关性分析"] = {
                "相关系数矩阵": corr_matrix.to_dict(),
                "强相关对": self._find_strong_correlations(corr_matrix)
            }
        
        # 异常值检测
        for col in numeric_cols:
            outliers = self._detect_outliers(df[col])
            analysis["异常值检测"][col] = {
                "异常值数量": len(outliers),
                "异常值比例": f"{len(outliers) / len(df) * 100:.2f}%"
            }
        
        return analysis
    
    def _create_visualizations(self, df: pd.DataFrame, user_prompt: str) -> List[Dict[str, Any]]:
        """创建可视化图表"""
        visualizations = []
        
        # 基础可视化
        basic_viz = DataAnalysisHelper.create_basic_visualizations(df)
        visualizations.extend(basic_viz)
        
        # 根据用户需求创建特定图表
        prompt_lower = user_prompt.lower()
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        # 趋势分析图
        if any(keyword in prompt_lower for keyword in ['趋势', 'trend', '时间', 'time']):
            date_cols = [col for col in df.columns if 'date' in col.lower() or 'time' in col.lower()]
            if date_cols and len(numeric_cols) > 0:
                date_col = date_cols[0]
                numeric_col = numeric_cols[0]
                try:
                    df_sorted = df.sort_values(date_col)
                    fig = px.line(df_sorted, x=date_col, y=numeric_col, title=f"{numeric_col} 时间趋势")
                    visualizations.append({
                        "type": "line",
                        "title": f"{numeric_col} 时间趋势",
                        "figure": fig
                    })
                except:
                    pass
        
        # 散点图分析
        if len(numeric_cols) >= 2 and any(keyword in prompt_lower for keyword in ['关系', 'relation', '散点', 'scatter']):
            fig = px.scatter(df, x=numeric_cols[0], y=numeric_cols[1], 
                           title=f"{numeric_cols[0]} vs {numeric_cols[1]}")
            visualizations.append({
                "type": "scatter",
                "title": f"{numeric_cols[0]} vs {numeric_cols[1]}",
                "figure": fig
            })
        
        # 箱线图
        if len(numeric_cols) > 0 and any(keyword in prompt_lower for keyword in ['分布', 'distribution', '箱线', 'box']):
            fig = go.Figure()
            for col in numeric_cols[:5]:  # 最多5列
                fig.add_trace(go.Box(y=df[col], name=col))
            fig.update_layout(title="数值列分布箱线图")
            visualizations.append({
                "type": "box",
                "title": "数值列分布箱线图",
                "figure": fig
            })
        
        return visualizations
    
    def _perform_advanced_analysis(self, df: pd.DataFrame, user_prompt: str) -> Dict[str, Any]:
        """执行高级分析"""
        analysis = {}
        
        prompt_lower = user_prompt.lower()
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        # 聚类分析
        if any(keyword in prompt_lower for keyword in ['聚类', 'cluster', '分组', 'group']):
            if len(numeric_cols) >= 2:
                analysis["聚类分析"] = self._perform_clustering(df[numeric_cols])
        
        # 主成分分析
        if any(keyword in prompt_lower for keyword in ['主成分', 'pca', '降维']):
            if len(numeric_cols) >= 3:
                analysis["主成分分析"] = self._perform_pca(df[numeric_cols])
        
        # 回归分析
        if any(keyword in prompt_lower for keyword in ['回归', 'regression', '预测', 'predict']):
            if len(numeric_cols) >= 2:
                analysis["回归分析"] = self._perform_regression(df[numeric_cols])
        
        return analysis
    
    def _test_normality(self, series: pd.Series) -> str:
        """简单的正态性检验"""
        try:
            from scipy import stats
            _, p_value = stats.normaltest(series.dropna())
            if p_value > 0.05:
                return "可能符合正态分布"
            else:
                return "不符合正态分布"
        except:
            return "无法检验"
    
    def _find_strong_correlations(self, corr_matrix: pd.DataFrame, threshold: float = 0.7) -> List[Dict[str, Any]]:
        """找出强相关关系"""
        strong_corrs = []
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                corr_value = corr_matrix.iloc[i, j]
                if abs(corr_value) > threshold:
                    strong_corrs.append({
                        "变量1": corr_matrix.columns[i],
                        "变量2": corr_matrix.columns[j],
                        "相关系数": round(corr_value, 3),
                        "相关强度": "强正相关" if corr_value > 0 else "强负相关"
                    })
        return strong_corrs
    
    def _detect_outliers(self, series: pd.Series) -> List[int]:
        """检测异常值"""
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers = series[(series < lower_bound) | (series > upper_bound)].index.tolist()
        return outliers
    
    def _perform_clustering(self, df_numeric: pd.DataFrame) -> Dict[str, Any]:
        """执行聚类分析"""
        try:
            from sklearn.cluster import KMeans
            from sklearn.preprocessing import StandardScaler
            
            # 标准化数据
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(df_numeric.fillna(df_numeric.mean()))
            
            # K-means聚类
            kmeans = KMeans(n_clusters=3, random_state=42)
            clusters = kmeans.fit_predict(scaled_data)
            
            return {
                "聚类数量": 3,
                "聚类标签": clusters.tolist(),
                "聚类中心": kmeans.cluster_centers_.tolist(),
                "惯性": float(kmeans.inertia_)
            }
        except Exception as e:
            return {"错误": f"聚类分析失败: {str(e)}"}
    
    def _perform_pca(self, df_numeric: pd.DataFrame) -> Dict[str, Any]:
        """执行主成分分析"""
        try:
            from sklearn.decomposition import PCA
            from sklearn.preprocessing import StandardScaler
            
            # 标准化数据
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(df_numeric.fillna(df_numeric.mean()))
            
            # PCA
            pca = PCA()
            pca.fit(scaled_data)
            
            return {
                "解释方差比": pca.explained_variance_ratio_.tolist(),
                "累积解释方差比": np.cumsum(pca.explained_variance_ratio_).tolist(),
                "主成分数量": len(pca.explained_variance_ratio_),
                "前两个主成分解释方差": f"{sum(pca.explained_variance_ratio_[:2]) * 100:.2f}%"
            }
        except Exception as e:
            return {"错误": f"主成分分析失败: {str(e)}"}
    
    def _perform_regression(self, df_numeric: pd.DataFrame) -> Dict[str, Any]:
        """执行回归分析"""
        try:
            from sklearn.linear_model import LinearRegression
            from sklearn.metrics import r2_score
            
            if len(df_numeric.columns) < 2:
                return {"错误": "需要至少2个数值列进行回归分析"}
            
            # 使用第一列作为目标变量，其他列作为特征
            y = df_numeric.iloc[:, 0].fillna(df_numeric.iloc[:, 0].mean())
            X = df_numeric.iloc[:, 1:].fillna(df_numeric.iloc[:, 1:].mean())
            
            # 线性回归
            model = LinearRegression()
            model.fit(X, y)
            y_pred = model.predict(X)
            
            return {
                "目标变量": df_numeric.columns[0],
                "特征变量": df_numeric.columns[1:].tolist(),
                "R²得分": float(r2_score(y, y_pred)),
                "回归系数": model.coef_.tolist(),
                "截距": float(model.intercept_)
            }
        except Exception as e:
            return {"错误": f"回归分析失败: {str(e)}"}
    
    def _generate_analysis_report(self, df: pd.DataFrame, statistical_analysis: Dict[str, Any],
                                advanced_analysis: Dict[str, Any], ai_analysis: str,
                                visualizations: List[Dict[str, Any]]) -> AnalysisReport:
        """生成分析报告"""
        
        # 生成摘要
        summary = f"""
数据包含 {len(df)} 行 {len(df.columns)} 列。
数值列数量: {len(df.select_dtypes(include=[np.number]).columns)}
分类列数量: {len(df.select_dtypes(include=['object']).columns)}
缺失值总数: {df.isnull().sum().sum()}
"""
        
        # 生成详细分析
        detailed_analysis = f"""
数据分析报告
============

分析时间: {TimeHelper.get_readable_time()}

数据概览:
{summary}

统计分析结果:
{json.dumps(statistical_analysis, indent=2, ensure_ascii=False)}

高级分析结果:
{json.dumps(advanced_analysis, indent=2, ensure_ascii=False)}

AI智能分析:
{ai_analysis}

可视化图表:
生成了 {len(visualizations)} 个图表，包括：
{chr(10).join([f"- {viz['title']}" for viz in visualizations])}
"""
        
        return AnalysisReport(
            title="数据分析报告",
            summary=summary,
            detailed_analysis=detailed_analysis,
            visualizations=[viz["title"] for viz in visualizations],
            raw_data_info={
                "shape": df.shape,
                "columns": df.columns.tolist(),
                "dtypes": df.dtypes.to_dict()
            }
        )
