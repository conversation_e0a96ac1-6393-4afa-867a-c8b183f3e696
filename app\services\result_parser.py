"""
结果解析服务
Result Parsing Service for Data Intelligence Agent
"""
import json
from docx import Document
from docx.shared import Inches
from typing import Dict, Any, List, Optional
from app.models.data_models import UploadedFile, ProcessingResult, TaskConfig
from app.models.ai_models import BaseAIModel
from app.utils.helpers import PromptHelper, TimeHelper
from app.utils.file_handler import FileHandler

class ResultParsingService:
    """结果解析服务"""
    
    def __init__(self, ai_model: BaseAIModel):
        self.ai_model = ai_model
    
    def parse_results(self, uploaded_file: UploadedFile, task_config: TaskConfig) -> ProcessingResult:
        """解析分析结果"""
        start_time = TimeHelper.get_readable_time()
        
        try:
            # 提取结果数据
            result_content = self._extract_result_content(uploaded_file)
            
            if not result_content:
                return ProcessingResult(
                    success=False,
                    error_message="无法从上传的文件中提取结果数据"
                )
            
            # 创建AI提示词
            prompt = PromptHelper.create_result_parsing_prompt(
                result_content, task_config.prompt
            )
            
            # 获取AI解析
            ai_interpretation = self.ai_model.generate_response(prompt)
            
            # 结构化解析结果
            structured_analysis = self._structure_analysis(result_content, ai_interpretation)
            
            # 生成Word文档
            doc_content = self._create_word_document(structured_analysis, ai_interpretation)
            
            # 保存结果
            output_files = []
            if task_config.output_path:
                # 保存Word文档
                doc_path = f"{task_config.output_path}/result_analysis_{TimeHelper.get_timestamp()}.docx"
                if self._save_word_document(doc_content, doc_path):
                    output_files.append(doc_path)
                
                # 保存结构化分析结果
                analysis_path = f"{task_config.output_path}/structured_analysis_{TimeHelper.get_timestamp()}.json"
                if FileHandler.save_text(json.dumps(structured_analysis, indent=2, ensure_ascii=False), analysis_path):
                    output_files.append(analysis_path)
            
            return ProcessingResult(
                success=True,
                result_data={
                    "structured_analysis": structured_analysis,
                    "ai_interpretation": ai_interpretation,
                    "word_document": doc_content,
                    "original_content": result_content
                },
                output_files=output_files,
                metadata={
                    "processing_time": start_time,
                    "content_length": len(result_content)
                }
            )
            
        except Exception as e:
            return ProcessingResult(
                success=False,
                error_message=f"结果解析失败: {str(e)}"
            )
    
    def _extract_result_content(self, uploaded_file: UploadedFile) -> str:
        """提取结果内容"""
        content = ""
        
        # 如果是文本内容
        if uploaded_file.text_content:
            content = uploaded_file.text_content
        
        # 如果是数据表格
        elif uploaded_file.dataframe is not None:
            df = uploaded_file.dataframe
            content = f"""
数据概览:
行数: {len(df)}
列数: {len(df.columns)}
列名: {', '.join(df.columns)}

数据样本:
{df.head(10).to_string()}

数据统计:
{df.describe().to_string()}
"""
        
        # 如果有元数据
        if uploaded_file.metadata:
            content += f"\n\n文件元数据:\n{json.dumps(uploaded_file.metadata, indent=2, ensure_ascii=False)}"
        
        return content
    
    def _structure_analysis(self, result_content: str, ai_interpretation: str) -> Dict[str, Any]:
        """结构化分析结果"""
        analysis = {
            "执行摘要": self._extract_executive_summary(ai_interpretation),
            "关键发现": self._extract_key_findings(ai_interpretation),
            "数据洞察": self._extract_data_insights(ai_interpretation),
            "趋势分析": self._extract_trends(ai_interpretation),
            "风险评估": self._extract_risks(ai_interpretation),
            "建议措施": self._extract_recommendations(ai_interpretation),
            "结论": self._extract_conclusions(ai_interpretation),
            "原始数据摘要": self._summarize_raw_data(result_content)
        }
        
        return analysis
    
    def _extract_executive_summary(self, ai_text: str) -> str:
        """提取执行摘要"""
        # 简单的关键词提取，实际应用中可以使用更复杂的NLP技术
        lines = ai_text.split('\n')
        summary_lines = []
        
        for line in lines[:10]:  # 取前10行作为摘要候选
            if any(keyword in line for keyword in ['总结', '概述', '摘要', '总体', '整体']):
                summary_lines.append(line.strip())
        
        if not summary_lines:
            # 如果没有找到明确的摘要，取前3句话
            sentences = ai_text.split('。')[:3]
            return '。'.join(sentences) + '。'
        
        return '\n'.join(summary_lines)
    
    def _extract_key_findings(self, ai_text: str) -> List[str]:
        """提取关键发现"""
        findings = []
        lines = ai_text.split('\n')
        
        for line in lines:
            if any(keyword in line for keyword in ['发现', '显示', '表明', '证明', '结果']):
                findings.append(line.strip())
        
        return findings[:5]  # 最多5个关键发现
    
    def _extract_data_insights(self, ai_text: str) -> List[str]:
        """提取数据洞察"""
        insights = []
        lines = ai_text.split('\n')
        
        for line in lines:
            if any(keyword in line for keyword in ['洞察', '模式', '规律', '特征', '趋势']):
                insights.append(line.strip())
        
        return insights[:5]
    
    def _extract_trends(self, ai_text: str) -> List[str]:
        """提取趋势分析"""
        trends = []
        lines = ai_text.split('\n')
        
        for line in lines:
            if any(keyword in line for keyword in ['趋势', '变化', '增长', '下降', '波动']):
                trends.append(line.strip())
        
        return trends[:3]
    
    def _extract_risks(self, ai_text: str) -> List[str]:
        """提取风险评估"""
        risks = []
        lines = ai_text.split('\n')
        
        for line in lines:
            if any(keyword in line for keyword in ['风险', '问题', '挑战', '威胁', '隐患']):
                risks.append(line.strip())
        
        return risks[:3]
    
    def _extract_recommendations(self, ai_text: str) -> List[str]:
        """提取建议措施"""
        recommendations = []
        lines = ai_text.split('\n')
        
        for line in lines:
            if any(keyword in line for keyword in ['建议', '推荐', '应该', '需要', '措施']):
                recommendations.append(line.strip())
        
        return recommendations[:5]
    
    def _extract_conclusions(self, ai_text: str) -> str:
        """提取结论"""
        lines = ai_text.split('\n')
        conclusion_lines = []
        
        for line in lines[-10:]:  # 从后10行中寻找结论
            if any(keyword in line for keyword in ['结论', '总结', '综上', '因此', '最终']):
                conclusion_lines.append(line.strip())
        
        if not conclusion_lines:
            # 如果没有找到明确的结论，取最后3句话
            sentences = ai_text.split('。')[-3:]
            return '。'.join(sentences)
        
        return '\n'.join(conclusion_lines)
    
    def _summarize_raw_data(self, result_content: str) -> Dict[str, Any]:
        """总结原始数据"""
        return {
            "内容长度": len(result_content),
            "行数": len(result_content.split('\n')),
            "包含数字": any(char.isdigit() for char in result_content),
            "包含表格": '|' in result_content or '\t' in result_content,
            "内容预览": result_content[:200] + "..." if len(result_content) > 200 else result_content
        }
    
    def _create_word_document(self, structured_analysis: Dict[str, Any], ai_interpretation: str) -> Document:
        """创建Word文档"""
        doc = Document()
        
        # 添加标题
        title = doc.add_heading('数据分析结果解析报告', 0)
        
        # 添加生成时间
        doc.add_paragraph(f'生成时间: {TimeHelper.get_readable_time()}')
        doc.add_paragraph('')
        
        # 执行摘要
        doc.add_heading('执行摘要', level=1)
        doc.add_paragraph(structured_analysis.get('执行摘要', ''))
        
        # 关键发现
        doc.add_heading('关键发现', level=1)
        findings = structured_analysis.get('关键发现', [])
        for i, finding in enumerate(findings, 1):
            doc.add_paragraph(f'{i}. {finding}')
        
        # 数据洞察
        doc.add_heading('数据洞察', level=1)
        insights = structured_analysis.get('数据洞察', [])
        for insight in insights:
            doc.add_paragraph(f'• {insight}')
        
        # 趋势分析
        doc.add_heading('趋势分析', level=1)
        trends = structured_analysis.get('趋势分析', [])
        for trend in trends:
            doc.add_paragraph(f'• {trend}')
        
        # 风险评估
        doc.add_heading('风险评估', level=1)
        risks = structured_analysis.get('风险评估', [])
        for risk in risks:
            doc.add_paragraph(f'• {risk}')
        
        # 建议措施
        doc.add_heading('建议措施', level=1)
        recommendations = structured_analysis.get('建议措施', [])
        for i, rec in enumerate(recommendations, 1):
            doc.add_paragraph(f'{i}. {rec}')
        
        # 结论
        doc.add_heading('结论', level=1)
        doc.add_paragraph(structured_analysis.get('结论', ''))
        
        # 附录：完整AI分析
        doc.add_heading('附录：完整AI分析', level=1)
        doc.add_paragraph(ai_interpretation)
        
        return doc
    
    def _save_word_document(self, doc: Document, filepath: str) -> bool:
        """保存Word文档"""
        try:
            import os
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            doc.save(filepath)
            return True
        except Exception as e:
            print(f"保存Word文档失败: {str(e)}")
            return False
