# 数智体 (Data Intelligence Agent)

<div align="center">
  <img src="app/static/logo.svg" alt="数智体Logo" width="200"/>
  
  **智能数据分析助手**
  
  [![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
  [![Streamlit](https://img.shields.io/badge/Streamlit-1.28+-red.svg)](https://streamlit.io)
  [![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
</div>

## 📖 项目简介

数智体是一个基于AI大模型的智能数据分析平台，能够根据用户的自然语言指令自动执行数据清洗、分析、解析和报告撰写等任务。

### 🎯 核心功能

1. **🧠 AI大模型集成**
   - 支持本地部署的Ollama模型
   - 支持云端API（OpenAI、Anthropic等）
   - 灵活的模型切换和配置

2. **🧹 智能数据清洗**
   - 自动识别数据质量问题
   - 基于AI建议的清洗策略
   - 支持多种文件格式

3. **📊 深度数据分析**
   - 统计分析和可视化
   - 相关性分析和异常检测
   - 聚类、PCA等高级分析

4. **📋 智能结果解析**
   - 自动解读分析结果
   - 生成结构化报告
   - 输出专业Word文档

5. **📝 自动文章撰写**
   - 基于数据生成分析文章
   - 学术和商业报告格式
   - 多格式输出支持

## 🚀 快速开始

### 环境要求

- Python 3.8+
- 8GB+ RAM
- 支持的操作系统：Windows、macOS、Linux

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd 数据分析智体
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置API密钥
```

4. **启动应用**
```bash
streamlit run app/main.py
```

5. **访问应用**
打开浏览器访问 `http://localhost:8501`

## ⚙️ 配置说明

### AI模型配置

#### 本地模型 (Ollama)
```bash
# 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 下载模型
ollama pull llama2
ollama pull codellama
```

#### 云端模型
在 `.env` 文件中配置API密钥：
```env
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
```

### 文件支持格式

- **数据文件**: CSV, Excel (.xlsx, .xls), JSON
- **文档文件**: PDF, Word (.docx), TXT
- **图像文件**: PNG, JPG, JPEG

## 📁 项目结构

```
数据分析智体/
├── app/                    # 主应用目录
│   ├── main.py            # Streamlit主应用
│   ├── config.py          # 配置文件
│   ├── models/            # 数据模型
│   │   ├── ai_models.py   # AI模型接口
│   │   └── data_models.py # 数据模型定义
│   ├── services/          # 业务服务
│   │   ├── data_cleaner.py    # 数据清洗服务
│   │   ├── data_analyzer.py   # 数据分析服务
│   │   ├── result_parser.py   # 结果解析服务
│   │   └── article_writer.py  # 文章撰写服务
│   ├── utils/             # 工具函数
│   │   ├── file_handler.py    # 文件处理
│   │   └── helpers.py         # 辅助函数
│   └── static/            # 静态资源
│       └── logo.svg       # 应用Logo
├── outputs/               # 输出目录
├── temp/                  # 临时文件目录
├── requirements.txt       # Python依赖
├── .env.example          # 环境变量示例
└── README.md             # 项目说明
```

## 🎮 使用指南

### 1. 数据清洗

1. 选择"数据清洗"功能
2. 上传数据文件（CSV、Excel等）
3. 输入清洗需求，例如：
   - "删除重复数据和异常值"
   - "处理缺失值并标准化列名"
4. 点击"开始处理"
5. 查看清洗结果和下载清洗后的数据

### 2. 数据分析

1. 选择"数据分析"功能
2. 上传要分析的数据文件
3. 描述分析需求，例如：
   - "分析销售数据的趋势和模式"
   - "找出影响用户满意度的关键因素"
4. 获取分析报告和可视化图表

### 3. 结果解析

1. 选择"结果解析"功能
2. 上传分析结果文件
3. 输入解析要求，例如：
   - "解释这些统计结果的业务含义"
   - "分析数据中的风险和机会"
4. 获取专业的解析报告

### 4. 文章撰写

1. 选择"文章撰写"功能
2. 上传相关数据或资料
3. 指定文章类型和要求，例如：
   - "撰写一篇关于市场分析的研究报告"
   - "基于数据写一篇商业洞察文章"
4. 获取格式化的文章文档

## 🔧 高级配置

### 自定义输出目录
```python
# 在config.py中修改
OUTPUT_DIR = "custom_outputs"
```

### 模型参数调整
```python
# 调整AI模型参数
response = ai_model.generate_response(
    prompt,
    max_tokens=2000,
    temperature=0.7
)
```

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持与反馈

- 📧 邮箱：<EMAIL>
- 🐛 问题反馈：[GitHub Issues](https://github.com/your-repo/issues)
- 💬 讨论交流：[GitHub Discussions](https://github.com/your-repo/discussions)

## 🙏 致谢

感谢以下开源项目的支持：
- [Streamlit](https://streamlit.io/) - Web应用框架
- [Pandas](https://pandas.pydata.org/) - 数据处理
- [Plotly](https://plotly.com/) - 数据可视化
- [OpenAI](https://openai.com/) - AI模型API
- [Ollama](https://ollama.ai/) - 本地AI模型

---

<div align="center">
  <strong>数智体 - 让数据分析更智能</strong>
</div>
