"""
数智体配置文件
Configuration file for Data Intelligence Agent
"""
import os
from typing import Dict, Any
from dotenv import load_dotenv

load_dotenv()

class Config:
    """应用配置类"""
    
    # 应用基本信息
    APP_NAME = "数智体"
    APP_VERSION = "1.0.0"
    APP_DESCRIPTION = "智能数据分析助手"
    
    # 文件上传配置
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    ALLOWED_EXTENSIONS = {
        'csv', 'xlsx', 'xls', 'json', 'txt', 'pdf', 
        'docx', 'doc', 'png', 'jpg', 'jpeg'
    }
    
    # 输出目录配置
    OUTPUT_DIR = "outputs"
    TEMP_DIR = "temp"
    
    # AI模型配置
    OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY", "")
    
    # 默认模型设置
    DEFAULT_LOCAL_MODEL = "qwen3:8b"
    DEFAULT_CLOUD_MODEL = "deepseek-r1:1.5b"
    
    # Streamlit配置
    STREAMLIT_CONFIG = {
        "page_title": "数智体 - 数据分析智能体",
        "page_icon": "🤖",
        "layout": "wide",
        "initial_sidebar_state": "expanded"
    }
    
    @classmethod
    def get_model_config(cls) -> Dict[str, Any]:
        """获取模型配置"""
        return {
            "ollama": {
                "base_url": cls.OLLAMA_BASE_URL,
                "models": ["llama2", "codellama", "mistral", "qwen"]
            },
            "openai": {
                "api_key": cls.OPENAI_API_KEY,
                "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"]
            },
            "anthropic": {
                "api_key": cls.ANTHROPIC_API_KEY,
                "models": ["claude-3-sonnet", "claude-3-haiku"]
            }
        }
    
    @classmethod
    def ensure_directories(cls):
        """确保必要的目录存在"""
        os.makedirs(cls.OUTPUT_DIR, exist_ok=True)
        os.makedirs(cls.TEMP_DIR, exist_ok=True)
