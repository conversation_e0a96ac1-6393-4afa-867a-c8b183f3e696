#!/usr/bin/env python3
"""
数智体简化版主应用
Simplified Main Application for Data Intelligence Agent
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import os
import sys
import json
from datetime import datetime
from typing import Dict, Any, List, Optional

# 页面配置
st.set_page_config(
    page_title="数智体 - 数据分析智能体",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

def render_header():
    """渲染页面头部"""
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown(
            '<h1 style="text-align: center; color: #2E86AB;">🤖 数智体</h1>',
            unsafe_allow_html=True
        )
        st.markdown(
            '<p style="text-align: center; color: #666;">Data Intelligence Agent</p>',
            unsafe_allow_html=True
        )
    
    st.markdown("---")
    
    # 功能介绍
    st.markdown("""
    ### 🎯 功能概览
    数智体是一个智能数据分析助手，提供以下核心功能：
    - 📊 **数据清洗**: 自动识别和处理数据质量问题
    - 📈 **数据分析**: 深度统计分析和可视化
    - 📋 **结果解析**: 智能解读分析结果
    - 📝 **文章撰写**: 自动生成专业分析报告
    """)

def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.header("⚙️ 配置设置")
        
        # AI模型选择
        st.subheader("🧠 AI模型选择")
        model_provider = st.selectbox(
            "选择模型提供商",
            ["本地模型(Ollama)", "OpenAI", "Anthropic"],
            help="选择要使用的AI模型提供商"
        )
        
        if model_provider == "本地模型(Ollama)":
            model_name = st.selectbox("选择模型", ["llama2", "codellama", "mistral"])
        elif model_provider == "OpenAI":
            model_name = st.selectbox("选择模型", ["gpt-3.5-turbo", "gpt-4"])
        else:
            model_name = st.selectbox("选择模型", ["claude-3-sonnet", "claude-3-haiku"])
        
        st.success(f"✅ 已选择: {model_provider} - {model_name}")
        
        st.markdown("---")
        
        # 输出设置
        st.subheader("📁 输出设置")
        output_dir = st.text_input("输出目录", value="outputs")
        
        if st.button("📂 创建输出目录"):
            os.makedirs(output_dir, exist_ok=True)
            st.success(f"目录已创建: {output_dir}")
        
        st.markdown("---")
        
        # 系统信息
        st.subheader("ℹ️ 系统信息")
        st.info(f"""
        **版本**: 1.0.0
        **当前时间**: {datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")}
        **Python版本**: {sys.version.split()[0]}
        """)

def process_uploaded_file(uploaded_file):
    """处理上传的文件"""
    if uploaded_file is None:
        return None
    
    file_details = {
        "filename": uploaded_file.name,
        "filetype": uploaded_file.type,
        "filesize": uploaded_file.size
    }
    
    try:
        if uploaded_file.name.endswith('.csv'):
            df = pd.read_csv(uploaded_file)
            file_details["dataframe"] = df
            file_details["rows"] = len(df)
            file_details["columns"] = len(df.columns)
        elif uploaded_file.name.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(uploaded_file)
            file_details["dataframe"] = df
            file_details["rows"] = len(df)
            file_details["columns"] = len(df.columns)
        elif uploaded_file.name.endswith('.json'):
            content = uploaded_file.read().decode('utf-8')
            data = json.loads(content)
            file_details["json_data"] = data
            if isinstance(data, list) and len(data) > 0 and isinstance(data[0], dict):
                df = pd.DataFrame(data)
                file_details["dataframe"] = df
        else:
            content = uploaded_file.read().decode('utf-8')
            file_details["text_content"] = content
    except Exception as e:
        file_details["error"] = str(e)
    
    return file_details

def create_sample_analysis(df):
    """创建示例分析"""
    analysis = {}
    
    # 基础统计
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 0:
        analysis["基础统计"] = df[numeric_cols].describe().to_dict()
    
    # 缺失值统计
    analysis["缺失值"] = df.isnull().sum().to_dict()
    
    # 数据类型
    analysis["数据类型"] = df.dtypes.astype(str).to_dict()
    
    return analysis

def create_visualizations(df):
    """创建可视化图表"""
    charts = []
    
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    categorical_cols = df.select_dtypes(include=['object']).columns
    
    # 数值列分布图
    for col in numeric_cols[:3]:  # 最多3个
        fig = px.histogram(df, x=col, title=f"{col} 分布图")
        charts.append({"title": f"{col} 分布图", "chart": fig})
    
    # 分类列计数图
    for col in categorical_cols[:2]:  # 最多2个
        if df[col].nunique() <= 10:
            value_counts = df[col].value_counts().head(10)
            fig = px.bar(x=value_counts.index, y=value_counts.values, title=f"{col} 计数图")
            charts.append({"title": f"{col} 计数图", "chart": fig})
    
    # 相关性热力图
    if len(numeric_cols) > 1:
        corr_matrix = df[numeric_cols].corr()
        fig = px.imshow(corr_matrix, text_auto=True, title="相关性热力图")
        charts.append({"title": "相关性热力图", "chart": fig})
    
    return charts

def main():
    """主函数"""
    render_header()
    render_sidebar()
    
    # 功能选择
    task_type = st.selectbox(
        "🎯 选择功能",
        ["数据清洗", "数据分析", "结果解析", "文章撰写"],
        help="选择要执行的数据处理任务"
    )
    
    # 文件上传
    uploaded_file = st.file_uploader(
        "📁 上传文件",
        type=['csv', 'xlsx', 'xls', 'json', 'txt'],
        help="支持多种文件格式"
    )
    
    if uploaded_file:
        # 处理文件
        file_details = process_uploaded_file(uploaded_file)
        
        if file_details:
            # 显示文件信息
            st.subheader("📄 文件信息")
            col1, col2 = st.columns(2)
            
            with col1:
                st.info(f"""
                **文件名**: {file_details['filename']}
                **文件类型**: {file_details['filetype']}
                **文件大小**: {file_details['filesize'] / 1024:.2f} KB
                """)
            
            with col2:
                if 'dataframe' in file_details:
                    st.info(f"""
                    **数据行数**: {file_details.get('rows', 'N/A')}
                    **数据列数**: {file_details.get('columns', 'N/A')}
                    **缺失值**: {file_details['dataframe'].isnull().sum().sum()}
                    """)
            
            # 显示数据预览
            if 'dataframe' in file_details:
                df = file_details['dataframe']
                st.subheader("👀 数据预览")
                st.dataframe(df.head())
                
                # 用户提示词
                user_prompt = st.text_area(
                    "💬 输入您的需求",
                    height=100,
                    placeholder="请详细描述您希望对数据进行什么样的处理...",
                )
                
                # 处理按钮
                if st.button("🚀 开始处理", type="primary"):
                    if user_prompt.strip():
                        with st.spinner("🔄 正在处理，请稍候..."):
                            # 模拟处理过程
                            import time
                            time.sleep(2)
                            
                            st.success("✅ 处理完成！")
                            
                            # 显示分析结果
                            if task_type == "数据分析":
                                st.subheader("📊 分析结果")
                                
                                # 统计分析
                                analysis = create_sample_analysis(df)
                                st.json(analysis)
                                
                                # 可视化
                                st.subheader("📈 数据可视化")
                                charts = create_visualizations(df)
                                
                                for chart_info in charts:
                                    st.plotly_chart(chart_info["chart"], use_container_width=True)
                                
                                # AI分析结果（模拟）
                                st.subheader("🤖 AI分析结果")
                                st.write(f"""
                                基于您的需求"{user_prompt}"，我对数据进行了深入分析：
                                
                                **主要发现**：
                                - 数据集包含 {len(df)} 行 {len(df.columns)} 列
                                - 数值列数量：{len(df.select_dtypes(include=[np.number]).columns)}
                                - 分类列数量：{len(df.select_dtypes(include=['object']).columns)}
                                - 缺失值总数：{df.isnull().sum().sum()}
                                
                                **建议**：
                                - 建议进一步清洗缺失值
                                - 可以考虑进行特征工程
                                - 数据质量整体良好，适合进行深度分析
                                """)
                            
                            elif task_type == "数据清洗":
                                st.subheader("🧹 数据清洗结果")
                                
                                # 模拟清洗
                                cleaned_df = df.dropna().drop_duplicates()
                                
                                st.write(f"""
                                **清洗前**：{len(df)} 行 {len(df.columns)} 列
                                **清洗后**：{len(cleaned_df)} 行 {len(cleaned_df.columns)} 列
                                **删除行数**：{len(df) - len(cleaned_df)}
                                """)
                                
                                st.dataframe(cleaned_df.head())
                            
                            elif task_type == "结果解析":
                                st.subheader("📋 结果解析")
                                st.write(f"""
                                根据您的需求"{user_prompt}"，我对数据进行了解析：
                                
                                这是一个包含 {len(df)} 条记录的数据集，具有以下特征：
                                - 数据完整性较好
                                - 适合进行统计分析
                                - 建议关注主要指标的变化趋势
                                """)
                            
                            elif task_type == "文章撰写":
                                st.subheader("📝 生成的文章")
                                st.markdown(f"""
                                # 数据分析报告
                                
                                ## 摘要
                                本报告基于用户需求"{user_prompt}"，对提供的数据集进行了全面分析。
                                
                                ## 数据概览
                                - 数据规模：{len(df)} 行 × {len(df.columns)} 列
                                - 数据质量：良好
                                - 主要字段：{', '.join(df.columns[:5])}
                                
                                ## 分析结果
                                通过对数据的深入分析，我们发现了以下关键洞察...
                                
                                ## 结论与建议
                                基于分析结果，我们建议...
                                """)
                    else:
                        st.error("❌ 请输入处理需求")
            
            elif 'text_content' in file_details:
                st.subheader("👀 文本内容预览")
                content = file_details['text_content']
                preview = content[:500] + "..." if len(content) > 500 else content
                st.text_area("内容预览", preview, height=200, disabled=True)
    
    # 底部信息
    st.markdown("---")
    st.markdown(
        '<div style="text-align: center; color: #666;">数智体 v1.0.0 - 让数据分析更智能</div>',
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main()
