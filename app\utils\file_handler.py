"""
文件处理工具
File Handler Utilities for Data Intelligence Agent
"""
import os
import pandas as pd
import json
import PyPDF2
from docx import Document
from PIL import Image
import io
from typing import Optional, Dict, Any, Tuple
from app.models.data_models import UploadedFile, FileType

class FileHandler:
    """文件处理器"""
    
    @staticmethod
    def detect_file_type(filename: str) -> FileType:
        """检测文件类型"""
        ext = filename.lower().split('.')[-1]
        
        if ext in ['csv']:
            return FileType.CSV
        elif ext in ['xlsx', 'xls']:
            return FileType.EXCEL
        elif ext in ['json']:
            return FileType.JSON
        elif ext in ['pdf']:
            return FileType.PDF
        elif ext in ['docx', 'doc']:
            return FileType.DOCX
        elif ext in ['txt']:
            return FileType.TXT
        elif ext in ['png', 'jpg', 'jpeg', 'gif', 'bmp']:
            return FileType.IMAGE
        else:
            return FileType.UNKNOWN
    
    @staticmethod
    def process_uploaded_file(file_content: bytes, filename: str) -> UploadedFile:
        """处理上传的文件"""
        file_type = FileHandler.detect_file_type(filename)
        
        uploaded_file = UploadedFile(
            name=filename,
            size=len(file_content),
            type=file_type,
            content=file_content
        )
        
        try:
            if file_type == FileType.CSV:
                uploaded_file.dataframe = pd.read_csv(io.BytesIO(file_content))
                uploaded_file.metadata = {
                    "rows": len(uploaded_file.dataframe),
                    "columns": len(uploaded_file.dataframe.columns),
                    "column_names": list(uploaded_file.dataframe.columns)
                }
            
            elif file_type == FileType.EXCEL:
                uploaded_file.dataframe = pd.read_excel(io.BytesIO(file_content))
                uploaded_file.metadata = {
                    "rows": len(uploaded_file.dataframe),
                    "columns": len(uploaded_file.dataframe.columns),
                    "column_names": list(uploaded_file.dataframe.columns)
                }
            
            elif file_type == FileType.JSON:
                json_data = json.loads(file_content.decode('utf-8'))
                uploaded_file.text_content = json.dumps(json_data, indent=2, ensure_ascii=False)
                if isinstance(json_data, list) and len(json_data) > 0 and isinstance(json_data[0], dict):
                    uploaded_file.dataframe = pd.DataFrame(json_data)
                uploaded_file.metadata = {"json_type": type(json_data).__name__}
            
            elif file_type == FileType.PDF:
                text_content = FileHandler._extract_pdf_text(file_content)
                uploaded_file.text_content = text_content
                uploaded_file.metadata = {"text_length": len(text_content)}
            
            elif file_type == FileType.DOCX:
                text_content = FileHandler._extract_docx_text(file_content)
                uploaded_file.text_content = text_content
                uploaded_file.metadata = {"text_length": len(text_content)}
            
            elif file_type == FileType.TXT:
                uploaded_file.text_content = file_content.decode('utf-8')
                uploaded_file.metadata = {"text_length": len(uploaded_file.text_content)}
            
            elif file_type == FileType.IMAGE:
                image = Image.open(io.BytesIO(file_content))
                uploaded_file.metadata = {
                    "width": image.width,
                    "height": image.height,
                    "format": image.format
                }
        
        except Exception as e:
            uploaded_file.metadata = {"error": str(e)}
        
        return uploaded_file
    
    @staticmethod
    def _extract_pdf_text(file_content: bytes) -> str:
        """从PDF提取文本"""
        try:
            pdf_reader = PyPDF2.PdfReader(io.BytesIO(file_content))
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            return text
        except Exception as e:
            return f"PDF文本提取失败: {str(e)}"
    
    @staticmethod
    def _extract_docx_text(file_content: bytes) -> str:
        """从DOCX提取文本"""
        try:
            doc = Document(io.BytesIO(file_content))
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text
        except Exception as e:
            return f"DOCX文本提取失败: {str(e)}"
    
    @staticmethod
    def save_dataframe(df: pd.DataFrame, filepath: str, format_type: str = "csv") -> bool:
        """保存DataFrame到文件"""
        try:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            if format_type.lower() == "csv":
                df.to_csv(filepath, index=False, encoding='utf-8-sig')
            elif format_type.lower() in ["xlsx", "excel"]:
                df.to_excel(filepath, index=False)
            elif format_type.lower() == "json":
                df.to_json(filepath, orient='records', force_ascii=False, indent=2)
            else:
                raise ValueError(f"不支持的格式: {format_type}")
            
            return True
        except Exception as e:
            print(f"保存文件失败: {str(e)}")
            return False
    
    @staticmethod
    def save_text(text: str, filepath: str) -> bool:
        """保存文本到文件"""
        try:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(text)
            return True
        except Exception as e:
            print(f"保存文本文件失败: {str(e)}")
            return False
    
    @staticmethod
    def get_file_info(uploaded_file: UploadedFile) -> Dict[str, Any]:
        """获取文件信息摘要"""
        info = {
            "文件名": uploaded_file.name,
            "文件大小": f"{uploaded_file.size / 1024:.2f} KB",
            "文件类型": uploaded_file.type.value
        }
        
        if uploaded_file.metadata:
            info.update(uploaded_file.metadata)
        
        if uploaded_file.dataframe is not None:
            info["数据预览"] = uploaded_file.dataframe.head().to_dict()
        
        return info
