#!/usr/bin/env python3
"""
数智体简单启动脚本
Simple startup script for Data Intelligence Agent
"""
import os
import sys
import subprocess

def main():
    """主函数"""
    print("🤖 数智体 (Data Intelligence Agent)")
    print("=" * 50)
    
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print(f"📁 工作目录: {script_dir}")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查Streamlit是否安装
    try:
        import streamlit
        print("✅ Streamlit已安装")
    except ImportError:
        print("❌ Streamlit未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "streamlit"])
            print("✅ Streamlit安装完成")
        except:
            print("❌ Streamlit安装失败，请手动安装: pip install streamlit")
            return
    
    # 启动应用
    print("\n🚀 正在启动数智体...")
    print("📍 应用将在浏览器中打开")
    print("💡 按 Ctrl+C 停止应用\n")
    
    try:
        # 使用streamlit run命令启动应用
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "app/main.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except FileNotFoundError:
        print("❌ 找不到app/main.py文件，请确保在正确的目录运行此脚本")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
