"""
AI模型接口
AI Models Interface for Data Intelligence Agent
"""
import requests
import json
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod
import openai
import anthropic
from app.config import Config

class BaseAIModel(ABC):
    """AI模型基类"""
    
    @abstractmethod
    def generate_response(self, prompt: str, **kwargs) -> str:
        """生成响应"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查模型是否可用"""
        pass

class OllamaModel(BaseAIModel):
    """Ollama本地模型"""
    
    def __init__(self, model_name: str = "llama2"):
        self.model_name = model_name
        self.base_url = Config.OLLAMA_BASE_URL
    
    def generate_response(self, prompt: str, **kwargs) -> str:
        """使用Ollama生成响应"""
        try:
            url = f"{self.base_url}/api/generate"
            data = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False
            }
            
            response = requests.post(url, json=data, timeout=60)
            response.raise_for_status()
            
            result = response.json()
            return result.get("response", "")
            
        except Exception as e:
            raise Exception(f"Ollama模型调用失败: {str(e)}")
    
    def is_available(self) -> bool:
        """检查Ollama是否可用"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return [model["name"] for model in data.get("models", [])]
            return []
        except:
            return []

class OpenAIModel(BaseAIModel):
    """OpenAI云端模型"""
    
    def __init__(self, model_name: str = "gpt-3.5-turbo"):
        self.model_name = model_name
        self.client = openai.OpenAI(api_key=Config.OPENAI_API_KEY)
    
    def generate_response(self, prompt: str, **kwargs) -> str:
        """使用OpenAI生成响应"""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=kwargs.get("max_tokens", 2000),
                temperature=kwargs.get("temperature", 0.7)
            )
            return response.choices[0].message.content
        except Exception as e:
            raise Exception(f"OpenAI模型调用失败: {str(e)}")
    
    def is_available(self) -> bool:
        """检查OpenAI API是否可用"""
        return bool(Config.OPENAI_API_KEY)

class AnthropicModel(BaseAIModel):
    """Anthropic Claude模型"""
    
    def __init__(self, model_name: str = "claude-3-sonnet-20240229"):
        self.model_name = model_name
        self.client = anthropic.Anthropic(api_key=Config.ANTHROPIC_API_KEY)
    
    def generate_response(self, prompt: str, **kwargs) -> str:
        """使用Anthropic生成响应"""
        try:
            response = self.client.messages.create(
                model=self.model_name,
                max_tokens=kwargs.get("max_tokens", 2000),
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text
        except Exception as e:
            raise Exception(f"Anthropic模型调用失败: {str(e)}")
    
    def is_available(self) -> bool:
        """检查Anthropic API是否可用"""
        return bool(Config.ANTHROPIC_API_KEY)

class AIModelManager:
    """AI模型管理器"""
    
    def __init__(self):
        self.models = {}
        self._initialize_models()
    
    def _initialize_models(self):
        """初始化可用模型"""
        # 初始化Ollama模型
        ollama = OllamaModel()
        if ollama.is_available():
            self.models["ollama"] = ollama
        
        # 初始化OpenAI模型
        openai_model = OpenAIModel()
        if openai_model.is_available():
            self.models["openai"] = openai_model
        
        # 初始化Anthropic模型
        anthropic_model = AnthropicModel()
        if anthropic_model.is_available():
            self.models["anthropic"] = anthropic_model
    
    def get_available_models(self) -> Dict[str, List[str]]:
        """获取所有可用模型"""
        available = {}
        
        if "ollama" in self.models:
            available["本地模型(Ollama)"] = self.models["ollama"].get_available_models()
        
        if "openai" in self.models:
            available["OpenAI"] = ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"]
        
        if "anthropic" in self.models:
            available["Anthropic"] = ["claude-3-sonnet", "claude-3-haiku"]
        
        return available
    
    def get_model(self, provider: str, model_name: str = None) -> BaseAIModel:
        """获取指定模型"""
        if provider == "ollama" and "ollama" in self.models:
            if model_name:
                return OllamaModel(model_name)
            return self.models["ollama"]
        
        elif provider == "openai" and "openai" in self.models:
            if model_name:
                return OpenAIModel(model_name)
            return self.models["openai"]
        
        elif provider == "anthropic" and "anthropic" in self.models:
            if model_name:
                return AnthropicModel(model_name)
            return self.models["anthropic"]
        
        raise ValueError(f"模型提供商 {provider} 不可用或未配置")
