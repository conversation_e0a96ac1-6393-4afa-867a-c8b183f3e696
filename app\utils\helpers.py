"""
辅助函数
Helper Functions for Data Intelligence Agent
"""
import time
import hashlib
import os
from datetime import datetime
from typing import Any, Dict, List, Optional
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

class DataAnalysisHelper:
    """数据分析辅助工具"""
    
    @staticmethod
    def generate_data_summary(df: pd.DataFrame) -> Dict[str, Any]:
        """生成数据摘要"""
        summary = {
            "基本信息": {
                "行数": len(df),
                "列数": len(df.columns),
                "内存使用": f"{df.memory_usage(deep=True).sum() / 1024:.2f} KB"
            },
            "列信息": {},
            "数据类型分布": df.dtypes.value_counts().to_dict(),
            "缺失值统计": df.isnull().sum().to_dict(),
            "数值列统计": {}
        }
        
        # 列详细信息
        for col in df.columns:
            col_info = {
                "数据类型": str(df[col].dtype),
                "非空值数量": df[col].count(),
                "缺失值数量": df[col].isnull().sum(),
                "唯一值数量": df[col].nunique()
            }
            
            if df[col].dtype in ['int64', 'float64']:
                col_info.update({
                    "最小值": df[col].min(),
                    "最大值": df[col].max(),
                    "平均值": df[col].mean(),
                    "中位数": df[col].median(),
                    "标准差": df[col].std()
                })
            
            summary["列信息"][col] = col_info
        
        # 数值列统计
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            summary["数值列统计"] = df[numeric_cols].describe().to_dict()
        
        return summary
    
    @staticmethod
    def create_basic_visualizations(df: pd.DataFrame) -> List[Dict[str, Any]]:
        """创建基础可视化图表"""
        visualizations = []
        
        # 数值列分布图
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            for col in numeric_cols[:5]:  # 最多5个数值列
                fig = px.histogram(df, x=col, title=f"{col} 分布图")
                visualizations.append({
                    "type": "histogram",
                    "title": f"{col} 分布图",
                    "figure": fig
                })
        
        # 分类列计数图
        categorical_cols = df.select_dtypes(include=['object']).columns
        for col in categorical_cols[:3]:  # 最多3个分类列
            if df[col].nunique() <= 20:  # 只显示唯一值不超过20的列
                value_counts = df[col].value_counts().head(10)
                fig = px.bar(x=value_counts.index, y=value_counts.values, 
                           title=f"{col} 计数图")
                visualizations.append({
                    "type": "bar",
                    "title": f"{col} 计数图",
                    "figure": fig
                })
        
        # 相关性热力图（如果有多个数值列）
        if len(numeric_cols) > 1:
            corr_matrix = df[numeric_cols].corr()
            fig = px.imshow(corr_matrix, text_auto=True, aspect="auto",
                          title="数值列相关性热力图")
            visualizations.append({
                "type": "heatmap",
                "title": "数值列相关性热力图",
                "figure": fig
            })
        
        return visualizations

class PromptHelper:
    """提示词辅助工具"""
    
    @staticmethod
    def create_data_cleaning_prompt(data_info: Dict[str, Any], user_prompt: str) -> str:
        """创建数据清洗提示词"""
        base_prompt = f"""
你是一个专业的数据清洗专家。请根据以下数据信息和用户需求，提供详细的数据清洗建议和步骤。

数据信息：
{data_info}

用户需求：
{user_prompt}

请提供：
1. 数据质量评估
2. 具体的清洗步骤
3. 需要注意的问题
4. 清洗后的数据结构建议

请用中文回答，并提供可执行的Python代码示例。
"""
        return base_prompt
    
    @staticmethod
    def create_data_analysis_prompt(data_info: Dict[str, Any], user_prompt: str) -> str:
        """创建数据分析提示词"""
        base_prompt = f"""
你是一个专业的数据分析师。请根据以下数据信息和用户需求，进行深入的数据分析。

数据信息：
{data_info}

用户需求：
{user_prompt}

请提供：
1. 数据概览和特征分析
2. 关键发现和洞察
3. 统计分析结果
4. 可视化建议
5. 结论和建议

请用中文回答，并提供详细的分析过程。
"""
        return base_prompt
    
    @staticmethod
    def create_result_parsing_prompt(result_data: str, user_prompt: str) -> str:
        """创建结果解析提示词"""
        base_prompt = f"""
你是一个专业的数据解读专家。请根据以下分析结果和用户需求，进行深入的解析和讨论。

分析结果：
{result_data}

用户需求：
{user_prompt}

请提供：
1. 结果解读和含义说明
2. 关键指标分析
3. 趋势和模式识别
4. 业务影响评估
5. 行动建议

请用中文回答，并以专业报告的形式呈现。
"""
        return base_prompt
    
    @staticmethod
    def create_article_writing_prompt(data_info: Dict[str, Any], user_prompt: str) -> str:
        """创建文章撰写提示词"""
        base_prompt = f"""
你是一个专业的数据科学写作专家。请根据以下数据信息和用户需求，撰写一篇高质量的分析文章。

数据信息：
{data_info}

用户需求：
{user_prompt}

请撰写一篇包含以下部分的文章：
1. 引言和背景
2. 数据来源和方法
3. 分析过程和发现
4. 结论和建议
5. 局限性和未来方向

文章要求：
- 逻辑清晰，结构完整
- 语言专业但易懂
- 包含具体的数据支撑
- 适合商业或学术场景

请用中文撰写。
"""
        return base_prompt

class TimeHelper:
    """时间辅助工具"""
    
    @staticmethod
    def get_timestamp() -> str:
        """获取时间戳字符串"""
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    @staticmethod
    def get_readable_time() -> str:
        """获取可读时间字符串"""
        return datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")
    
    @staticmethod
    def measure_time(func):
        """装饰器：测量函数执行时间"""
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            return result, execution_time
        return wrapper

class FileHelper:
    """文件辅助工具"""
    
    @staticmethod
    def generate_filename(prefix: str, extension: str) -> str:
        """生成唯一文件名"""
        timestamp = TimeHelper.get_timestamp()
        return f"{prefix}_{timestamp}.{extension}"
    
    @staticmethod
    def get_file_hash(content: bytes) -> str:
        """获取文件内容的哈希值"""
        return hashlib.md5(content).hexdigest()
    
    @staticmethod
    def ensure_directory(filepath: str) -> str:
        """确保目录存在"""
        directory = os.path.dirname(filepath)
        if directory:
            os.makedirs(directory, exist_ok=True)
        return filepath
