#!/usr/bin/env python3
"""
数智体启动器
Data Intelligence Agent Launcher
"""
import os
import sys
import subprocess

def show_banner():
    """显示启动横幅"""
    print("🤖" + "=" * 58 + "🤖")
    print("🤖                数智体启动器                    🤖")
    print("🤖           Data Intelligence Agent            🤖")
    print("🤖                 v1.0.0                       🤖")
    print("🤖" + "=" * 58 + "🤖")
    print()

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要3.8+")
        return False
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查必要的包
    required_packages = ['streamlit', 'pandas', 'plotly', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n📦 正在安装缺失的包...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install"
            ] + missing_packages)
            print("✅ 依赖包安装完成")
        except:
            print("❌ 依赖包安装失败")
            return False
    
    return True

def show_menu():
    """显示版本选择菜单"""
    print("\n📋 请选择要启动的版本:")
    print("1. 🚀 演示版本 (推荐) - 功能完整，运行稳定")
    print("2. 🔧 简化版本 - 基础功能，快速体验")
    print("3. 🏗️ 完整版本 - 高级功能，需要配置")
    print("4. 🧪 系统测试 - 检查系统功能")
    print("5. ❌ 退出")
    print()

def launch_version(choice):
    """启动选择的版本"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    if choice == "1":
        print("🚀 启动演示版本...")
        if os.path.exists("demo.py"):
            subprocess.run([
                sys.executable, "-m", "streamlit", "run", 
                "demo.py", "--server.port", "8501"
            ])
        else:
            print("❌ 找不到demo.py文件")
    
    elif choice == "2":
        print("🔧 启动简化版本...")
        if os.path.exists("main_simple.py"):
            subprocess.run([
                sys.executable, "-m", "streamlit", "run", 
                "main_simple.py", "--server.port", "8501"
            ])
        else:
            print("❌ 找不到main_simple.py文件")
    
    elif choice == "3":
        print("🏗️ 启动完整版本...")
        if os.path.exists("app/main.py"):
            subprocess.run([
                sys.executable, "-m", "streamlit", "run", 
                "app/main.py", "--server.port", "8501"
            ])
        else:
            print("❌ 找不到app/main.py文件")
    
    elif choice == "4":
        print("🧪 运行系统测试...")
        if os.path.exists("test_system.py"):
            subprocess.run([sys.executable, "test_system.py"])
        else:
            print("❌ 找不到test_system.py文件")
    
    elif choice == "5":
        print("👋 再见！")
        return False
    
    else:
        print("❌ 无效选择，请重新选择")
    
    return True

def main():
    """主函数"""
    show_banner()
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        input("按回车键退出...")
        return
    
    print("\n✅ 环境检查通过")
    
    # 主循环
    while True:
        show_menu()
        
        try:
            choice = input("请输入选择 (1-5): ").strip()
            
            if choice in ["1", "2", "3", "4", "5"]:
                if not launch_version(choice):
                    break
                
                if choice in ["1", "2", "3"]:
                    print("\n🌐 应用已启动，请在浏览器中访问: http://localhost:8501")
                    print("💡 按 Ctrl+C 停止应用")
                    break
                
                if choice == "4":
                    input("\n按回车键继续...")
            else:
                print("❌ 请输入1-5之间的数字")
        
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
