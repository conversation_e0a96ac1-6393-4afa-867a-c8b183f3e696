"""
数智体主应用
Main Application for Data Intelligence Agent
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import os
import sys
import time
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 导入应用模块
from app.config import Config
from app.models.ai_models import AIModelManager
from app.models.data_models import TaskType, TaskConfig, UploadedFile
from app.services.data_cleaner import DataCleaningService
from app.services.data_analyzer import DataAnalysisService
from app.services.result_parser import ResultParsingService
from app.services.article_writer import ArticleWritingService
from app.utils.file_handler import FileHandler
from app.utils.helpers import TimeHelper

# 页面配置
st.set_page_config(**Config.STREAMLIT_CONFIG)

# 确保必要目录存在
Config.ensure_directories()

class DataIntelligenceApp:
    """数智体主应用类"""

    def __init__(self):
        self.model_manager = AIModelManager()
        self.initialize_session_state()

    def initialize_session_state(self):
        """初始化会话状态"""
        if 'uploaded_file' not in st.session_state:
            st.session_state.uploaded_file = None
        if 'processing_result' not in st.session_state:
            st.session_state.processing_result = None
        if 'selected_model' not in st.session_state:
            st.session_state.selected_model = None

    def run(self):
        """运行主应用"""
        self.render_header()
        self.render_sidebar()
        self.render_main_content()

    def render_header(self):
        """渲染页面头部"""
        col1, col2, col3 = st.columns([1, 2, 1])

        with col2:
            # 显示Logo
            if os.path.exists("app/static/logo.svg"):
                with open("app/static/logo.svg", "r", encoding="utf-8") as f:
                    logo_svg = f.read()
                st.markdown(f'<div style="text-align: center;">{logo_svg}</div>', unsafe_allow_html=True)
            else:
                st.markdown(
                    '<h1 style="text-align: center; color: #2E86AB;">🤖 数智体</h1>',
                    unsafe_allow_html=True
                )
                st.markdown(
                    '<p style="text-align: center; color: #666;">Data Intelligence Agent</p>',
                    unsafe_allow_html=True
                )

        st.markdown("---")

        # 功能介绍
        st.markdown("""
        ### 🎯 功能概览
        数智体是一个智能数据分析助手，提供以下核心功能：
        - 📊 **数据清洗**: 自动识别和处理数据质量问题
        - 📈 **数据分析**: 深度统计分析和可视化
        - 📋 **结果解析**: 智能解读分析结果
        - 📝 **文章撰写**: 自动生成专业分析报告
        """)

    def render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            st.header("⚙️ 配置设置")

            # AI模型选择
            self.render_model_selection()

            st.markdown("---")

            # 输出设置
            st.subheader("📁 输出设置")
            output_dir = st.text_input(
                "输出目录",
                value=Config.OUTPUT_DIR,
                help="指定结果文件的保存目录"
            )

            if st.button("📂 创建输出目录"):
                os.makedirs(output_dir, exist_ok=True)
                st.success(f"目录已创建: {output_dir}")

            st.markdown("---")

            # 系统信息
            st.subheader("ℹ️ 系统信息")
            st.info(f"""
            **版本**: {Config.APP_VERSION}
            **当前时间**: {TimeHelper.get_readable_time()}
            **可用模型**: {len(self.model_manager.get_available_models())}
            """)

    def render_model_selection(self):
        """渲染模型选择界面"""
        st.subheader("🧠 AI模型选择")

        available_models = self.model_manager.get_available_models()

        if not available_models:
            st.error("❌ 没有可用的AI模型，请检查配置")
            return

        # 选择模型提供商
        providers = list(available_models.keys())
        selected_provider = st.selectbox(
            "选择模型提供商",
            providers,
            help="选择要使用的AI模型提供商"
        )

        # 选择具体模型
        if selected_provider and available_models[selected_provider]:
            models = available_models[selected_provider]
            selected_model = st.selectbox(
                "选择具体模型",
                models,
                help="选择要使用的具体模型"
            )

            # 保存选择
            st.session_state.selected_model = {
                "provider": selected_provider,
                "model": selected_model
            }

            st.success(f"✅ 已选择: {selected_provider} - {selected_model}")
        else:
            st.warning("⚠️ 该提供商暂无可用模型")

    def render_main_content(self):
        """渲染主要内容区域"""
        # 功能选择
        task_type = st.selectbox(
            "🎯 选择功能",
            ["数据清洗", "数据分析", "结果解析", "文章撰写"],
            help="选择要执行的数据处理任务"
        )

        # 文件上传
        uploaded_file = st.file_uploader(
            "📁 上传文件",
            type=['csv', 'xlsx', 'xls', 'json', 'txt', 'pdf', 'docx'],
            help="支持多种文件格式，包括CSV、Excel、JSON、PDF、Word等"
        )

        if uploaded_file:
            # 处理上传的文件
            file_content = uploaded_file.read()
            processed_file = FileHandler.process_uploaded_file(file_content, uploaded_file.name)
            st.session_state.uploaded_file = processed_file

            # 显示文件信息
            self.display_file_info(processed_file)

        # 用户提示词输入
        user_prompt = st.text_area(
            "💬 输入您的需求",
            height=100,
            placeholder="请详细描述您希望对数据进行什么样的处理...",
            help="清晰描述您的需求，AI将根据您的指令进行相应的数据处理"
        )

        # 执行按钮
        if st.button("🚀 开始处理", type="primary"):
            if not st.session_state.uploaded_file:
                st.error("❌ 请先上传文件")
            elif not user_prompt.strip():
                st.error("❌ 请输入处理需求")
            elif not st.session_state.selected_model:
                st.error("❌ 请先选择AI模型")
            else:
                self.process_data(task_type, user_prompt)

        # 显示处理结果
        if st.session_state.processing_result:
            self.display_results()

    def display_file_info(self, uploaded_file: UploadedFile):
        """显示文件信息"""
        st.subheader("📄 文件信息")

        file_info = FileHandler.get_file_info(uploaded_file)

        col1, col2 = st.columns(2)
        with col1:
            st.info(f"""
            **文件名**: {file_info.get('文件名', 'N/A')}
            **文件大小**: {file_info.get('文件大小', 'N/A')}
            **文件类型**: {file_info.get('文件类型', 'N/A')}
            """)

        with col2:
            if uploaded_file.dataframe is not None:
                st.info(f"""
                **数据行数**: {file_info.get('rows', 'N/A')}
                **数据列数**: {file_info.get('columns', 'N/A')}
                **缺失值**: {uploaded_file.dataframe.isnull().sum().sum()}
                """)

        # 显示数据预览
        if uploaded_file.dataframe is not None:
            st.subheader("👀 数据预览")
            st.dataframe(uploaded_file.dataframe.head())
        elif uploaded_file.text_content:
            st.subheader("👀 内容预览")
            preview_text = uploaded_file.text_content[:500]
            if len(uploaded_file.text_content) > 500:
                preview_text += "..."
            st.text_area("内容预览", preview_text, height=150, disabled=True)

    def process_data(self, task_type: str, user_prompt: str):
        """处理数据"""
        with st.spinner("🔄 正在处理，请稍候..."):
            try:
                # 获取AI模型
                model_info = st.session_state.selected_model
                ai_model = self.model_manager.get_model(
                    model_info["provider"].lower().replace("本地模型(ollama)", "ollama"),
                    model_info["model"]
                )

                # 创建任务配置
                task_config = TaskConfig(
                    task_type=self._get_task_type(task_type),
                    prompt=user_prompt,
                    model_provider=model_info["provider"],
                    model_name=model_info["model"],
                    output_path=Config.OUTPUT_DIR
                )

                # 根据任务类型选择服务
                if task_type == "数据清洗":
                    service = DataCleaningService(ai_model)
                    result = service.clean_data(st.session_state.uploaded_file, task_config)
                elif task_type == "数据分析":
                    service = DataAnalysisService(ai_model)
                    result = service.analyze_data(st.session_state.uploaded_file, task_config)
                elif task_type == "结果解析":
                    service = ResultParsingService(ai_model)
                    result = service.parse_results(st.session_state.uploaded_file, task_config)
                elif task_type == "文章撰写":
                    service = ArticleWritingService(ai_model)
                    result = service.write_article(st.session_state.uploaded_file, task_config)

                st.session_state.processing_result = result

                if result.success:
                    st.success("✅ 处理完成！")
                else:
                    st.error(f"❌ 处理失败: {result.error_message}")

            except Exception as e:
                st.error(f"❌ 处理过程中发生错误: {str(e)}")

    def _get_task_type(self, task_name: str) -> TaskType:
        """获取任务类型枚举"""
        mapping = {
            "数据清洗": TaskType.DATA_CLEANING,
            "数据分析": TaskType.DATA_ANALYSIS,
            "结果解析": TaskType.RESULT_PARSING,
            "文章撰写": TaskType.ARTICLE_WRITING
        }
        return mapping.get(task_name, TaskType.DATA_ANALYSIS)

    def display_results(self):
        """显示处理结果"""
        result = st.session_state.processing_result

        if not result.success:
            return

        st.subheader("📊 处理结果")

        # 显示输出文件
        if result.output_files:
            st.success(f"✅ 已生成 {len(result.output_files)} 个输出文件:")
            for file_path in result.output_files:
                st.write(f"📄 {file_path}")

        # 根据任务类型显示特定结果
        if result.result_data:
            self._display_task_specific_results(result.result_data)

    def _display_task_specific_results(self, result_data: Dict[str, Any]):
        """显示任务特定的结果"""
        # 显示可视化图表
        if "visualizations" in result_data:
            st.subheader("📈 可视化图表")
            for viz in result_data["visualizations"]:
                if viz.get("figure"):
                    st.plotly_chart(viz["figure"], use_container_width=True)

        # 显示清洗后的数据
        if "cleaned_dataframe" in result_data:
            st.subheader("🧹 清洗后的数据")
            st.dataframe(result_data["cleaned_dataframe"].head())

        # 显示AI分析结果
        if "ai_analysis" in result_data:
            st.subheader("🤖 AI分析结果")
            st.write(result_data["ai_analysis"])

        # 显示统计分析
        if "statistical_analysis" in result_data:
            st.subheader("📊 统计分析")
            st.json(result_data["statistical_analysis"])

def main():
    """主函数"""
    app = DataIntelligenceApp()
    app.run()

if __name__ == "__main__":
    main()
