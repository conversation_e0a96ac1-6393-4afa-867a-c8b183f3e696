#!/bin/bash

echo "🤖 数智体 (Data Intelligence Agent)"
echo "====================================="
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python 3.8+"
    echo "Ubuntu/Debian: sudo apt install python3 python3-pip python3-venv"
    echo "CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "macOS: brew install python3"
    exit 1
fi

echo "✅ Python环境检查通过"

# 检查是否存在虚拟环境
if [ ! -d "venv" ]; then
    echo "📦 创建虚拟环境..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "❌ 虚拟环境创建失败"
        exit 1
    fi
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 升级pip
echo "⬆️ 升级pip..."
pip install --upgrade pip

# 安装依赖
echo "📦 安装依赖包..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

# 运行系统测试
echo "🧪 运行系统测试..."
python test_system.py
if [ $? -ne 0 ]; then
    echo "⚠️ 系统测试发现问题，但仍可尝试启动应用"
fi

echo
echo "🚀 启动数智体应用..."
echo "📍 应用将在浏览器中自动打开"
echo "💡 按 Ctrl+C 停止应用"
echo

# 启动应用
python run.py
