#!/usr/bin/env python3
"""
数智体演示版本
Demo version of Data Intelligence Agent
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
from datetime import datetime

# 页面配置
st.set_page_config(
    page_title="数智体 - 数据分析智能体",
    page_icon="🤖",
    layout="wide"
)

def main():
    """主函数"""
    # 页面标题
    st.title("🤖 数智体")
    st.subheader("Data Intelligence Agent - 智能数据分析助手")
    
    st.markdown("---")
    
    # 功能介绍
    st.markdown("""
    ### 🎯 核心功能
    - 📊 **数据清洗**: 自动处理数据质量问题
    - 📈 **数据分析**: 深度统计分析和可视化  
    - 📋 **结果解析**: 智能解读分析结果
    - 📝 **文章撰写**: 自动生成分析报告
    """)
    
    # 侧边栏配置
    with st.sidebar:
        st.header("⚙️ 配置")
        
        # 模型选择
        model_provider = st.selectbox(
            "AI模型",
            ["本地模型", "OpenAI", "Anthropic"]
        )
        
        # 功能选择
        task_type = st.selectbox(
            "选择功能",
            ["数据分析", "数据清洗", "结果解析", "文章撰写"]
        )
        
        st.markdown("---")
        st.info(f"当前时间: {datetime.now().strftime('%H:%M:%S')}")
    
    # 主要内容区域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader(f"📊 {task_type}")
        
        # 文件上传
        uploaded_file = st.file_uploader(
            "上传数据文件",
            type=['csv', 'xlsx', 'json', 'txt'],
            help="支持CSV、Excel、JSON、TXT格式"
        )
        
        if uploaded_file:
            # 显示文件信息
            st.success(f"✅ 文件已上传: {uploaded_file.name}")
            
            # 处理CSV文件
            if uploaded_file.name.endswith('.csv'):
                try:
                    df = pd.read_csv(uploaded_file)
                    
                    # 显示数据预览
                    st.subheader("📋 数据预览")
                    st.dataframe(df.head())
                    
                    # 显示基本信息
                    st.subheader("ℹ️ 数据信息")
                    info_col1, info_col2, info_col3 = st.columns(3)
                    
                    with info_col1:
                        st.metric("行数", len(df))
                    with info_col2:
                        st.metric("列数", len(df.columns))
                    with info_col3:
                        st.metric("缺失值", df.isnull().sum().sum())
                    
                    # 用户需求输入
                    user_prompt = st.text_area(
                        "描述您的分析需求",
                        placeholder="例如：分析销售数据的趋势和模式",
                        height=100
                    )
                    
                    # 处理按钮
                    if st.button("🚀 开始分析", type="primary"):
                        if user_prompt.strip():
                            with st.spinner("正在分析中..."):
                                # 模拟处理时间
                                import time
                                time.sleep(2)
                                
                                st.success("✅ 分析完成！")
                                
                                # 显示分析结果
                                st.subheader("📈 分析结果")
                                
                                # 基础统计
                                numeric_cols = df.select_dtypes(include=[np.number]).columns
                                if len(numeric_cols) > 0:
                                    st.write("**数值列统计**")
                                    st.dataframe(df[numeric_cols].describe())
                                
                                # 创建可视化
                                if len(numeric_cols) > 0:
                                    st.write("**数据可视化**")
                                    
                                    # 选择要可视化的列
                                    selected_col = st.selectbox(
                                        "选择要可视化的列",
                                        numeric_cols
                                    )
                                    
                                    # 创建直方图
                                    fig = px.histogram(
                                        df, 
                                        x=selected_col, 
                                        title=f"{selected_col} 分布图"
                                    )
                                    st.plotly_chart(fig, use_container_width=True)
                                
                                # AI分析结果
                                st.subheader("🤖 AI分析结果")
                                st.write(f"""
                                **分析摘要**：
                                根据您的需求"{user_prompt}"，我对数据进行了分析：
                                
                                - 数据集包含 {len(df)} 行 {len(df.columns)} 列
                                - 数据质量：{'良好' if df.isnull().sum().sum() < len(df) * 0.1 else '需要清洗'}
                                - 主要特征：数值型变量 {len(numeric_cols)} 个
                                
                                **建议**：
                                - 数据整体结构完整，适合进行深入分析
                                - 建议关注主要指标的分布特征
                                - 可以进一步探索变量间的相关关系
                                """)
                        else:
                            st.error("请输入分析需求")
                
                except Exception as e:
                    st.error(f"文件处理失败: {str(e)}")
            
            else:
                st.info("请上传CSV格式的数据文件进行分析")
    
    with col2:
        st.subheader("📊 系统状态")
        
        # 系统信息
        st.metric("AI模型", model_provider)
        st.metric("当前功能", task_type)
        
        # 使用提示
        st.subheader("💡 使用提示")
        st.markdown("""
        1. 选择AI模型和功能
        2. 上传数据文件
        3. 描述分析需求
        4. 点击开始分析
        5. 查看结果和建议
        """)
        
        # 示例数据
        if st.button("📥 下载示例数据"):
            # 创建示例数据
            sample_data = pd.DataFrame({
                '日期': pd.date_range('2024-01-01', periods=30),
                '销售额': np.random.normal(10000, 2000, 30),
                '客户数': np.random.poisson(50, 30),
                '地区': np.random.choice(['北京', '上海', '广州'], 30)
            })
            
            # 转换为CSV
            csv = sample_data.to_csv(index=False, encoding='utf-8-sig')
            
            st.download_button(
                label="下载示例数据",
                data=csv,
                file_name="sample_data.csv",
                mime="text/csv"
            )
    
    # 底部信息
    st.markdown("---")
    st.markdown("**数智体 v1.0.0** - 让数据分析更智能 🚀")

if __name__ == "__main__":
    main()
