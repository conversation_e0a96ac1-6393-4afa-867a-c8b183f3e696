<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="100" cy="100" r="95" fill="url(#gradient1)" stroke="#2E86AB" stroke-width="3"/>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E86AB;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F39C12;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E67E22;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 数据图表元素 -->
  <g transform="translate(30, 60)">
    <!-- 柱状图 -->
    <rect x="10" y="40" width="8" height="20" fill="#FFFFFF" opacity="0.8"/>
    <rect x="20" y="30" width="8" height="30" fill="#FFFFFF" opacity="0.8"/>
    <rect x="30" y="35" width="8" height="25" fill="#FFFFFF" opacity="0.8"/>
    <rect x="40" y="25" width="8" height="35" fill="#FFFFFF" opacity="0.8"/>
    
    <!-- 折线图 -->
    <polyline points="60,50 70,40 80,45 90,35 100,30" 
              stroke="#FFFFFF" stroke-width="2" fill="none" opacity="0.9"/>
    <circle cx="60" cy="50" r="2" fill="#FFFFFF"/>
    <circle cx="70" cy="40" r="2" fill="#FFFFFF"/>
    <circle cx="80" cy="45" r="2" fill="#FFFFFF"/>
    <circle cx="90" cy="35" r="2" fill="#FFFFFF"/>
    <circle cx="100" cy="30" r="2" fill="#FFFFFF"/>
  </g>
  
  <!-- 中央AI大脑图标 -->
  <g transform="translate(100, 100)">
    <!-- 大脑轮廓 -->
    <path d="M-20,-15 Q-25,-25 -15,-30 Q0,-35 15,-30 Q25,-25 20,-15 Q25,-5 20,5 Q15,15 0,20 Q-15,15 -20,5 Q-25,-5 -20,-15 Z" 
          fill="url(#gradient2)" stroke="#FFFFFF" stroke-width="2"/>
    
    <!-- 大脑纹理 -->
    <path d="M-15,-10 Q-10,-15 -5,-10 Q0,-5 5,-10 Q10,-15 15,-10" 
          stroke="#FFFFFF" stroke-width="1.5" fill="none" opacity="0.7"/>
    <path d="M-15,0 Q-10,5 -5,0 Q0,5 5,0 Q10,5 15,0" 
          stroke="#FFFFFF" stroke-width="1.5" fill="none" opacity="0.7"/>
    <path d="M-10,10 Q-5,15 0,10 Q5,15 10,10" 
          stroke="#FFFFFF" stroke-width="1.5" fill="none" opacity="0.7"/>
  </g>
  
  <!-- 数据流动效果 -->
  <g opacity="0.6">
    <circle cx="50" cy="80" r="3" fill="#FFFFFF">
      <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="150" cy="120" r="3" fill="#FFFFFF">
      <animate attributeName="opacity" values="0;1;0" dur="2s" begin="0.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="80" cy="150" r="3" fill="#FFFFFF">
      <animate attributeName="opacity" values="0;1;0" dur="2s" begin="1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="120" cy="50" r="3" fill="#FFFFFF">
      <animate attributeName="opacity" values="0;1;0" dur="2s" begin="1.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 文字标识 -->
  <text x="100" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2E86AB">数智体</text>
  <text x="100" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Data Intelligence Agent</text>
</svg>
