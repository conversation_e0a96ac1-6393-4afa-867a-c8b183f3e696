#!/usr/bin/env python3
"""
测试多文件上传功能
Test multiple file upload functionality
"""
import sys
import os
import pandas as pd

# 添加app目录到路径
sys.path.append('app')

def test_multiple_files():
    """测试多文件处理功能"""
    print("🧪 测试多文件上传功能")
    print("=" * 60)
    
    # 测试文件列表
    test_files = [
        'test_data/sample_sales_data.csv',
        'test_data/product_data.csv', 
        'test_data/customer_data.csv',
        'test_data/analysis_report.txt'
    ]
    
    print(f"📁 准备测试 {len(test_files)} 个文件:")
    for i, file_path in enumerate(test_files, 1):
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path) / 1024
            print(f"  {i}. {os.path.basename(file_path)} ({file_size:.2f} KB) ✅")
        else:
            print(f"  {i}. {os.path.basename(file_path)} ❌ 文件不存在")
    
    print(f"\n🔍 分析各文件内容:")
    
    # 分析每个文件
    processed_files = []
    
    for file_path in test_files:
        if not os.path.exists(file_path):
            continue
            
        print(f"\n📄 处理文件: {os.path.basename(file_path)}")
        
        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
                print(f"  - 类型: CSV数据文件")
                print(f"  - 行数: {len(df)}")
                print(f"  - 列数: {len(df.columns)}")
                print(f"  - 列名: {list(df.columns)}")
                print(f"  - 缺失值: {df.isnull().sum().sum()}")
                
                processed_files.append({
                    "文件名": os.path.basename(file_path),
                    "类型": "CSV",
                    "行数": len(df),
                    "列数": len(df.columns),
                    "状态": "✅ 成功"
                })
                
            elif file_path.endswith('.txt'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"  - 类型: 文本文件")
                print(f"  - 字符数: {len(content)}")
                print(f"  - 行数: {len(content.split(chr(10)))}")
                print(f"  - 内容预览: {content[:100]}...")
                
                processed_files.append({
                    "文件名": os.path.basename(file_path),
                    "类型": "TXT",
                    "行数": len(content.split('\n')),
                    "列数": "N/A",
                    "状态": "✅ 成功"
                })
                
        except Exception as e:
            print(f"  - ❌ 处理失败: {str(e)}")
            processed_files.append({
                "文件名": os.path.basename(file_path),
                "类型": "未知",
                "行数": "N/A",
                "列数": "N/A", 
                "状态": "❌ 失败"
            })
    
    # 生成处理结果概览
    print(f"\n📊 文件处理结果概览:")
    print("-" * 80)
    print(f"{'序号':<4} {'文件名':<25} {'类型':<8} {'行数':<8} {'列数':<8} {'状态':<10}")
    print("-" * 80)
    
    for i, file_info in enumerate(processed_files, 1):
        print(f"{i:<4} {file_info['文件名']:<25} {file_info['类型']:<8} {str(file_info['行数']):<8} {str(file_info['列数']):<8} {file_info['状态']:<10}")
    
    print("-" * 80)
    
    # 统计信息
    total_files = len(processed_files)
    success_files = sum(1 for f in processed_files if "成功" in f["状态"])
    csv_files = sum(1 for f in processed_files if f["类型"] == "CSV")
    txt_files = sum(1 for f in processed_files if f["类型"] == "TXT")
    
    print(f"\n📈 统计信息:")
    print(f"  - 总文件数: {total_files}")
    print(f"  - 成功处理: {success_files}")
    print(f"  - CSV文件: {csv_files}")
    print(f"  - TXT文件: {txt_files}")
    print(f"  - 成功率: {success_files/total_files*100:.1f}%")
    
    # 测试文件选择逻辑
    print(f"\n🎯 测试文件选择逻辑:")
    
    if csv_files > 0:
        print(f"  - 如果用户选择数据分析，推荐使用CSV文件")
        csv_file_names = [f["文件名"] for f in processed_files if f["类型"] == "CSV"]
        print(f"  - 可选CSV文件: {', '.join(csv_file_names)}")
    
    if txt_files > 0:
        print(f"  - 如果用户选择结果解析，推荐使用TXT文件")
        txt_file_names = [f["文件名"] for f in processed_files if f["类型"] == "TXT"]
        print(f"  - 可选TXT文件: {', '.join(txt_file_names)}")
    
    # 模拟用户操作场景
    print(f"\n🎮 模拟用户操作场景:")
    
    scenarios = [
        {
            "场景": "批量数据分析",
            "描述": "用户上传多个CSV文件，想要对比分析不同数据集",
            "推荐操作": "选择主要数据文件进行分析，其他文件作为对比参考"
        },
        {
            "场景": "综合报告生成", 
            "描述": "用户上传数据文件和已有分析报告，想要生成综合报告",
            "推荐操作": "先分析数据文件，再结合文本报告进行综合分析"
        },
        {
            "场景": "数据验证",
            "描述": "用户上传多个相关数据文件，想要验证数据一致性",
            "推荐操作": "逐个分析各文件，对比关键指标的一致性"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n  场景 {i}: {scenario['场景']}")
        print(f"    描述: {scenario['描述']}")
        print(f"    推荐: {scenario['推荐操作']}")
    
    print(f"\n✅ 多文件上传功能测试完成!")
    print(f"\n💡 使用建议:")
    print(f"  1. 上传多个文件时，系统会显示文件选择器")
    print(f"  2. 用户可以通过下拉菜单选择要处理的文件")
    print(f"  3. 系统会显示所有文件的概览表格")
    print(f"  4. 根据任务类型，系统会推荐合适的文件类型")
    
    return processed_files

if __name__ == "__main__":
    test_multiple_files()
