"""
数据清洗服务
Data Cleaning Service for Data Intelligence Agent
"""
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from app.models.data_models import UploadedFile, ProcessingResult, TaskConfig
from app.models.ai_models import BaseAIModel
from app.utils.helpers import PromptHelper, DataAnalysisHelper, TimeHelper
from app.utils.file_handler import FileHandler

class DataCleaningService:
    """数据清洗服务"""

    def __init__(self, ai_model: BaseAIModel):
        self.ai_model = ai_model

    def clean_data(self, uploaded_file: UploadedFile, task_config: TaskConfig) -> ProcessingResult:
        """执行数据清洗"""
        start_time = TimeHelper.get_readable_time()

        try:
            # 检查是否有可处理的数据
            if uploaded_file.dataframe is None:
                return ProcessingResult(
                    success=False,
                    error_message="无法从上传的文件中提取数据表格"
                )

            df = uploaded_file.dataframe.copy()

            # 生成数据摘要
            data_summary = DataAnalysisHelper.generate_data_summary(df)

            # 创建AI提示词
            prompt = PromptHelper.create_data_cleaning_prompt(
                data_summary, task_config.prompt
            )

            # 获取AI建议
            ai_response = self.ai_model.generate_response(prompt)

            # 执行基础清洗操作
            cleaned_df, cleaning_steps = self._perform_basic_cleaning(df)

            # 根据AI建议执行高级清洗
            final_df, advanced_steps = self._perform_ai_guided_cleaning(
                cleaned_df, ai_response, task_config.prompt
            )

            # 生成清洗报告
            cleaning_report = self._generate_cleaning_report(
                df, final_df, cleaning_steps + advanced_steps, ai_response
            )

            # 保存结果
            output_files = []
            if task_config.output_path:
                # 保存清洗后的数据
                cleaned_file_path = f"{task_config.output_path}/cleaned_data_{TimeHelper.get_timestamp()}.csv"
                if FileHandler.save_dataframe(final_df, cleaned_file_path):
                    output_files.append(cleaned_file_path)

                # 保存清洗报告
                report_path = f"{task_config.output_path}/cleaning_report_{TimeHelper.get_timestamp()}.txt"
                if FileHandler.save_text(cleaning_report, report_path):
                    output_files.append(report_path)

            return ProcessingResult(
                success=True,
                result_data={
                    "cleaned_dataframe": final_df,
                    "cleaning_report": cleaning_report,
                    "ai_suggestions": ai_response,
                    "original_shape": df.shape,
                    "cleaned_shape": final_df.shape
                },
                output_files=output_files,
                metadata={
                    "processing_time": start_time,
                    "cleaning_steps": len(cleaning_steps + advanced_steps)
                }
            )

        except Exception as e:
            return ProcessingResult(
                success=False,
                error_message=f"数据清洗失败: {str(e)}"
            )

    def _perform_basic_cleaning(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """执行基础清洗操作"""
        cleaned_df = df.copy()
        steps = []

        # 1. 删除完全重复的行
        initial_rows = len(cleaned_df)
        cleaned_df = cleaned_df.drop_duplicates()
        if len(cleaned_df) < initial_rows:
            steps.append(f"删除了 {initial_rows - len(cleaned_df)} 行重复数据")

        # 2. 处理明显的数据类型问题
        for col in cleaned_df.columns:
            if cleaned_df[col].dtype == 'object':
                # 尝试转换数值列
                try:
                    # 检查是否可以转换为数值
                    numeric_series = pd.to_numeric(cleaned_df[col], errors='coerce')
                    if numeric_series.notna().sum() > len(cleaned_df) * 0.8:  # 80%以上可转换
                        cleaned_df[col] = numeric_series
                        steps.append(f"将列 '{col}' 转换为数值类型")
                except:
                    pass

                # 尝试转换日期列
                if any(keyword in col.lower() for keyword in ['date', 'time', '日期', '时间']):
                    try:
                        cleaned_df[col] = pd.to_datetime(cleaned_df[col], errors='coerce')
                        steps.append(f"将列 '{col}' 转换为日期类型")
                    except:
                        pass

        # 3. 处理明显的异常值（仅数值列）
        numeric_cols = cleaned_df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            Q1 = cleaned_df[col].quantile(0.25)
            Q3 = cleaned_df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 3 * IQR  # 使用3倍IQR作为极端异常值标准
            upper_bound = Q3 + 3 * IQR

            outliers = cleaned_df[(cleaned_df[col] < lower_bound) | (cleaned_df[col] > upper_bound)]
            if len(outliers) > 0 and len(outliers) < len(cleaned_df) * 0.05:  # 异常值少于5%
                cleaned_df = cleaned_df[(cleaned_df[col] >= lower_bound) & (cleaned_df[col] <= upper_bound)]
                steps.append(f"删除了列 '{col}' 中的 {len(outliers)} 个极端异常值")

        return cleaned_df, steps

    def _perform_ai_guided_cleaning(self, df: pd.DataFrame, ai_response: str, user_prompt: str) -> Tuple[pd.DataFrame, List[str]]:
        """根据AI建议执行高级清洗"""
        cleaned_df = df.copy()
        steps = []

        # 这里可以根据AI响应中的建议执行更复杂的清洗操作
        # 目前实现一些基于用户提示词的常见清洗操作

        prompt_lower = user_prompt.lower()

        # 特殊处理：提取特定地区的数据
        if "广州" in user_prompt and ("提取" in user_prompt or "筛选" in user_prompt):
            if "地区" in cleaned_df.columns:
                original_count = len(cleaned_df)
                cleaned_df = cleaned_df[cleaned_df["地区"] == "广州"]
                steps.append(f"提取地区为广州的数据，从 {original_count} 行筛选出 {len(cleaned_df)} 行")
            else:
                steps.append("未找到'地区'列，无法按地区筛选")

        # 处理其他地区筛选
        for region in ["北京", "上海", "深圳"]:
            if region in user_prompt and ("提取" in user_prompt or "筛选" in user_prompt):
                if "地区" in cleaned_df.columns:
                    original_count = len(cleaned_df)
                    cleaned_df = cleaned_df[cleaned_df["地区"] == region]
                    steps.append(f"提取地区为{region}的数据，从 {original_count} 行筛选出 {len(cleaned_df)} 行")

        # 处理缺失值
        if any(keyword in prompt_lower for keyword in ['缺失', 'missing', 'null', 'nan']):
            for col in cleaned_df.columns:
                missing_count = cleaned_df[col].isnull().sum()
                if missing_count > 0:
                    if cleaned_df[col].dtype in ['int64', 'float64']:
                        # 数值列用中位数填充
                        cleaned_df[col].fillna(cleaned_df[col].median(), inplace=True)
                        steps.append(f"用中位数填充列 '{col}' 的 {missing_count} 个缺失值")
                    else:
                        # 分类列用众数填充
                        mode_value = cleaned_df[col].mode()
                        if len(mode_value) > 0:
                            cleaned_df[col].fillna(mode_value[0], inplace=True)
                            steps.append(f"用众数填充列 '{col}' 的 {missing_count} 个缺失值")

        # 标准化列名
        if any(keyword in prompt_lower for keyword in ['标准化', 'standardize', '规范']):
            old_columns = cleaned_df.columns.tolist()
            new_columns = [col.strip().replace(' ', '_').lower() for col in old_columns]
            if old_columns != new_columns:
                cleaned_df.columns = new_columns
                steps.append("标准化了列名格式")

        return cleaned_df, steps

    def _generate_cleaning_report(self, original_df: pd.DataFrame, cleaned_df: pd.DataFrame,
                                steps: List[str], ai_response: str) -> str:
        """生成清洗报告"""
        report = f"""
数据清洗报告
============

清洗时间: {TimeHelper.get_readable_time()}

原始数据概况:
- 行数: {len(original_df)}
- 列数: {len(original_df.columns)}
- 缺失值总数: {original_df.isnull().sum().sum()}

清洗后数据概况:
- 行数: {len(cleaned_df)}
- 列数: {len(cleaned_df.columns)}
- 缺失值总数: {cleaned_df.isnull().sum().sum()}

执行的清洗步骤:
{chr(10).join([f"- {step}" for step in steps])}

AI分析建议:
{ai_response}

数据质量改善:
- 数据完整性: {((cleaned_df.count().sum() / cleaned_df.size) * 100):.2f}%
- 行数变化: {len(cleaned_df) - len(original_df)} ({((len(cleaned_df) - len(original_df)) / len(original_df) * 100):.2f}%)
"""
        return report
