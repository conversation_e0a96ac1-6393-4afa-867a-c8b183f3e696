"""
数据模型
Data Models for Data Intelligence Agent
"""
from dataclasses import dataclass
from typing import Optional, Dict, Any, List
from enum import Enum
import pandas as pd

class TaskType(Enum):
    """任务类型枚举"""
    DATA_CLEANING = "data_cleaning"
    DATA_ANALYSIS = "data_analysis"
    RESULT_PARSING = "result_parsing"
    ARTICLE_WRITING = "article_writing"

class FileType(Enum):
    """文件类型枚举"""
    CSV = "csv"
    EXCEL = "excel"
    JSON = "json"
    PDF = "pdf"
    DOCX = "docx"
    TXT = "txt"
    IMAGE = "image"
    UNKNOWN = "unknown"

@dataclass
class UploadedFile:
    """上传文件信息"""
    name: str
    size: int
    type: FileType
    content: Optional[bytes] = None
    dataframe: Optional[pd.DataFrame] = None
    text_content: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class TaskConfig:
    """任务配置"""
    task_type: TaskType
    prompt: str
    model_provider: str
    model_name: str
    output_format: str = "default"
    output_path: Optional[str] = None
    additional_params: Optional[Dict[str, Any]] = None

@dataclass
class ProcessingResult:
    """处理结果"""
    success: bool
    result_data: Optional[Any] = None
    output_files: Optional[List[str]] = None
    error_message: Optional[str] = None
    processing_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class AnalysisReport:
    """分析报告"""
    title: str
    summary: str
    detailed_analysis: str
    visualizations: Optional[List[str]] = None
    recommendations: Optional[str] = None
    raw_data_info: Optional[Dict[str, Any]] = None
    processing_steps: Optional[List[str]] = None
