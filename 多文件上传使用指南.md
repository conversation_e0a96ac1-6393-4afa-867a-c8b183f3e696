# 📁 数智体多文件上传使用指南

## 🎯 功能概述

数智体现在支持同时上传多个文件，让您可以：
- 批量处理多个数据文件
- 对比分析不同数据集
- 综合多种数据源进行分析
- 灵活选择要处理的文件

## 🚀 使用方法

### 1. 上传多个文件

1. **点击文件上传区域**
   - 在主界面找到"📁 上传文件"区域
   - 点击或拖拽文件到上传区域

2. **选择多个文件**
   - 按住 `Ctrl` (Windows) 或 `Cmd` (Mac) 键
   - 点击选择多个文件
   - 或者拖拽多个文件到上传区域

3. **支持的文件格式**
   - CSV文件 (.csv)
   - Excel文件 (.xlsx, .xls)
   - JSON文件 (.json)
   - 文本文件 (.txt)
   - PDF文件 (.pdf)
   - Word文档 (.docx)

### 2. 文件管理界面

上传多个文件后，系统会显示：

#### 📊 文件概览表格
```
序号 | 文件名              | 类型 | 大小    | 行数 | 列数 | 状态
-----|-------------------|------|---------|------|------|--------
1    | sales_data.csv    | CSV  | 5.78 KB | 100  | 6    | ✅ 成功
2    | product_data.csv  | CSV  | 0.57 KB | 10   | 6    | ✅ 成功
3    | customer_data.csv | CSV  | 0.72 KB | 15   | 7    | ✅ 成功
4    | report.txt        | TXT  | 1.21 KB | 39   | N/A  | ✅ 成功
```

#### 🎛️ 文件选择器
- 下拉菜单显示所有上传的文件
- 选择要处理的文件
- 当前选择的文件会显示详细信息

#### 📈 统计信息
- 总文件数
- 数据文件数量
- 文本文件数量
- 处理成功率

### 3. 文件处理流程

1. **选择处理文件**
   - 使用下拉菜单选择要处理的文件
   - 系统会显示选中文件的详细信息

2. **查看文件信息**
   - 文件基本信息（名称、大小、类型）
   - 数据概览（行数、列数、缺失值）
   - 数据预览（前几行数据）

3. **执行分析任务**
   - 选择分析功能（数据清洗、分析、解析、撰写）
   - 输入分析需求
   - 开始处理

## 📋 使用场景

### 场景1: 批量数据分析
**适用情况**: 有多个相关的数据文件需要分析
```
上传文件:
- sales_2023_q1.csv
- sales_2023_q2.csv  
- sales_2023_q3.csv
- sales_2023_q4.csv

操作流程:
1. 上传所有季度销售数据
2. 选择Q1数据进行基础分析
3. 切换到其他季度数据进行对比
4. 生成年度综合分析报告
```

### 场景2: 多维度数据整合
**适用情况**: 需要结合不同维度的数据进行综合分析
```
上传文件:
- sales_data.csv      (销售数据)
- customer_data.csv   (客户数据)
- product_data.csv    (产品数据)
- market_report.txt   (市场报告)

操作流程:
1. 先分析销售数据，了解整体趋势
2. 分析客户数据，了解用户画像
3. 分析产品数据，了解产品表现
4. 结合市场报告，生成综合洞察
```

### 场景3: 数据验证和清洗
**适用情况**: 需要验证多个数据源的一致性
```
上传文件:
- raw_data.csv        (原始数据)
- cleaned_data.csv    (清洗后数据)
- validation_rules.txt (验证规则)

操作流程:
1. 分析原始数据，识别数据质量问题
2. 对比清洗后数据，验证清洗效果
3. 根据验证规则，确认数据准确性
```

## 💡 使用技巧

### 1. 文件命名建议
- 使用有意义的文件名
- 包含日期或版本信息
- 避免特殊字符和空格

### 2. 文件大小优化
- 单个文件建议不超过100MB
- 大文件可以分割后上传
- 删除不必要的列和行

### 3. 数据格式统一
- CSV文件使用UTF-8编码
- 确保列名一致性
- 日期格式统一

### 4. 处理顺序建议
- 先处理主要数据文件
- 再处理辅助数据文件
- 最后进行综合分析

## ⚠️ 注意事项

### 文件限制
- 最大文件大小: 100MB
- 同时上传文件数: 建议不超过10个
- 支持格式: CSV, Excel, JSON, TXT, PDF, Word

### 性能考虑
- 大量文件可能影响处理速度
- 建议分批上传和处理
- 及时清理不需要的文件

### 数据安全
- 所有文件仅在本地处理
- 不会上传到云端服务器
- 处理完成后可以删除临时文件

## 🔧 故障排除

### 问题1: 文件上传失败
**解决方案**:
- 检查文件格式是否支持
- 确认文件大小不超过限制
- 尝试重新上传

### 问题2: 文件处理异常
**解决方案**:
- 检查文件内容格式
- 确认文件编码为UTF-8
- 查看错误提示信息

### 问题3: 选择器不显示文件
**解决方案**:
- 刷新页面重新上传
- 检查文件是否成功上传
- 确认文件处理状态

## 📞 获取帮助

如果遇到问题，可以：
1. 查看系统提示信息
2. 运行测试脚本验证功能
3. 查看详细的错误日志

---

**数智体 v1.0.0** - 多文件上传功能让数据分析更高效！ 🚀
