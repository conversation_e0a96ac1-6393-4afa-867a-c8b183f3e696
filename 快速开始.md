# 🚀 数智体快速开始指南

## 📋 一键启动

### 方法一：使用启动器（推荐）
```bash
python 启动数智体.py
```
然后按照菜单提示选择版本。

### 方法二：直接启动演示版
```bash
streamlit run demo.py
```

### 方法三：使用批处理文件（Windows）
双击 `start.bat` 文件

## 🎯 版本说明

### 🚀 演示版本（推荐）
- **文件**: `demo.py`
- **特点**: 功能完整，界面美观，运行稳定
- **适用**: 新用户体验，日常使用
- **启动**: `streamlit run demo.py`

### 🔧 简化版本
- **文件**: `main_simple.py`
- **特点**: 基础功能，快速响应
- **适用**: 快速测试，基础分析
- **启动**: `streamlit run main_simple.py`

### 🏗️ 完整版本
- **文件**: `app/main.py`
- **特点**: 高级功能，AI集成
- **适用**: 专业用户，复杂分析
- **启动**: `streamlit run app/main.py`

## 📊 功能演示

### 1. 数据上传
- 支持格式：CSV、Excel、JSON、TXT
- 拖拽上传或点击选择
- 自动识别文件类型

### 2. 数据分析
- 基础统计分析
- 数据可视化
- 缺失值检测
- 分布分析

### 3. AI智能分析
- 自然语言需求描述
- 智能分析建议
- 结果解读
- 报告生成

## 🎮 使用步骤

1. **启动应用**
   ```bash
   python 启动数智体.py
   ```

2. **选择版本**
   - 推荐选择"演示版本"

3. **打开浏览器**
   - 自动打开 http://localhost:8501
   - 或手动访问该地址

4. **上传数据**
   - 点击"上传数据文件"
   - 选择CSV格式的数据文件

5. **描述需求**
   - 在文本框中输入分析需求
   - 例如："分析销售数据的趋势"

6. **开始分析**
   - 点击"🚀 开始分析"按钮
   - 等待处理完成

7. **查看结果**
   - 查看数据统计
   - 查看可视化图表
   - 阅读AI分析结果

## 📥 示例数据

### 获取示例数据
1. 在应用右侧点击"📥 下载示例数据"
2. 下载 `sample_data.csv` 文件
3. 使用该文件进行功能测试

### 或使用系统生成的测试数据
```bash
python test_system.py
```
会在 `test_data/` 目录生成示例数据。

## 🔧 故障排除

### 问题1：应用启动失败
**解决方案**：
```bash
pip install streamlit pandas plotly numpy
python 启动数智体.py
```

### 问题2：浏览器无法访问
**解决方案**：
- 检查防火墙设置
- 手动访问 http://localhost:8501
- 尝试使用 http://127.0.0.1:8501

### 问题3：文件上传失败
**解决方案**：
- 检查文件格式（推荐CSV）
- 确保文件大小不超过100MB
- 检查文件编码（推荐UTF-8）

### 问题4：分析结果异常
**解决方案**：
- 检查数据格式是否正确
- 确保数据包含数值列
- 重新描述分析需求

## 💡 使用技巧

### 1. 数据准备
- 使用CSV格式获得最佳兼容性
- 确保列名清晰明确
- 处理明显的数据错误

### 2. 需求描述
- 使用具体的分析目标
- 描述期望的输出类型
- 提及关注的关键指标

### 3. 结果解读
- 关注数据质量评估
- 查看可视化图表
- 阅读AI分析建议

## 📞 获取帮助

### 文档资源
- `README.md` - 项目详细说明
- `使用说明.md` - 完整使用指南
- `项目总结.md` - 项目开发总结

### 系统测试
```bash
python test_system.py
```

### 常见问题
- 检查Python版本（需要3.8+）
- 确保网络连接正常
- 查看控制台错误信息

## 🎉 开始使用

现在您可以开始使用数智体进行数据分析了！

```bash
python 启动数智体.py
```

选择"演示版本"，上传您的数据，开始智能分析之旅！

---

**数智体 v1.0.0** - 让数据分析更智能 🚀
