@echo off
chcp 65001 >nul
echo.
echo 🤖 数智体 (Data Intelligence Agent)
echo =====================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

REM 检查是否存在虚拟环境
if not exist "venv" (
    echo 📦 创建虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat

REM 安装依赖
echo 📦 安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

REM 运行系统测试
echo 🧪 运行系统测试...
python test_system.py
if errorlevel 1 (
    echo ⚠️ 系统测试发现问题，但仍可尝试启动应用
)

echo.
echo 🚀 启动数智体应用...
echo 📍 应用将在浏览器中自动打开
echo 💡 按 Ctrl+C 停止应用
echo.

REM 启动应用
python run.py

pause
