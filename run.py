#!/usr/bin/env python3
"""
数智体启动脚本
Startup script for Data Intelligence Agent
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        sys.exit(1)
    print(f"✅ Python版本检查通过: {sys.version}")

def check_dependencies():
    """检查依赖包"""
    try:
        import streamlit
        import pandas
        import plotly
        print("✅ 核心依赖包检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def setup_environment():
    """设置环境"""
    # 创建必要的目录
    directories = ['outputs', 'temp', 'uploads']
    for dir_name in directories:
        os.makedirs(dir_name, exist_ok=True)
        print(f"✅ 目录已创建: {dir_name}")
    
    # 检查环境变量文件
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            print("⚠️  未找到.env文件，请复制.env.example并配置")
        else:
            print("⚠️  未找到环境配置文件")

def install_dependencies():
    """安装依赖包"""
    print("📦 正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖包安装失败")
        return False

def start_app(port=8501, host="localhost"):
    """启动应用"""
    print(f"🚀 正在启动数智体...")
    print(f"📍 访问地址: http://{host}:{port}")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "app/main.py",
            "--server.port", str(port),
            "--server.address", host,
            "--server.headless", "true"
        ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="数智体启动脚本")
    parser.add_argument("--port", type=int, default=8501, help="端口号 (默认: 8501)")
    parser.add_argument("--host", default="localhost", help="主机地址 (默认: localhost)")
    parser.add_argument("--install", action="store_true", help="安装依赖包")
    parser.add_argument("--check", action="store_true", help="仅检查环境")
    
    args = parser.parse_args()
    
    print("🤖 数智体 (Data Intelligence Agent)")
    print("=" * 50)
    
    # 检查Python版本
    check_python_version()
    
    # 安装依赖包
    if args.install:
        if not install_dependencies():
            sys.exit(1)
    
    # 检查依赖
    if not check_dependencies():
        if not args.install:
            print("💡 提示: 使用 --install 参数自动安装依赖")
        sys.exit(1)
    
    # 设置环境
    setup_environment()
    
    # 仅检查环境
    if args.check:
        print("✅ 环境检查完成")
        return
    
    # 启动应用
    start_app(args.port, args.host)

if __name__ == "__main__":
    main()
