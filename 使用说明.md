# 数智体使用说明

## 🚀 快速启动

### 方法一：使用简化版本（推荐）
```bash
python start_simple.py
```

### 方法二：使用完整版本
```bash
python start_app.py
```

### 方法三：直接使用Streamlit
```bash
streamlit run main_simple.py
```

## 📋 系统要求

- Python 3.8+
- 8GB+ RAM
- 网络连接（用于安装依赖包）

## 🔧 安装依赖

如果遇到依赖包问题，请手动安装：

```bash
pip install streamlit pandas plotly numpy openpyxl python-docx
```

## 📖 功能说明

### 1. 数据清洗
- 上传CSV、Excel文件
- 自动检测并处理缺失值
- 删除重复数据
- 数据类型转换

### 2. 数据分析
- 基础统计分析
- 数据可视化
- 相关性分析
- 分布分析

### 3. 结果解析
- 智能解读分析结果
- 生成结构化报告
- 提供业务洞察

### 4. 文章撰写
- 基于数据生成分析报告
- 自动生成摘要和结论
- 专业格式输出

## 🎯 使用步骤

1. **启动应用**
   - 运行启动脚本
   - 等待浏览器自动打开

2. **选择功能**
   - 在主界面选择要使用的功能
   - 配置AI模型（如果需要）

3. **上传文件**
   - 点击"上传文件"按钮
   - 选择要分析的数据文件
   - 支持CSV、Excel、JSON、TXT格式

4. **输入需求**
   - 在文本框中描述您的分析需求
   - 例如："分析销售数据的趋势"

5. **开始处理**
   - 点击"开始处理"按钮
   - 等待处理完成

6. **查看结果**
   - 查看分析结果和可视化图表
   - 下载生成的报告文件

## 📊 支持的文件格式

- **CSV文件** (.csv)
- **Excel文件** (.xlsx, .xls)
- **JSON文件** (.json)
- **文本文件** (.txt)

## 🔍 示例数据

系统会自动生成测试数据，位于 `test_data/sample_sales_data.csv`，您可以用它来测试各项功能。

## ⚠️ 注意事项

1. **文件大小限制**：建议上传文件不超过100MB
2. **数据隐私**：所有数据仅在本地处理，不会上传到云端
3. **AI模型**：简化版本使用模拟AI响应，完整版本需要配置真实的AI模型

## 🐛 常见问题

### Q: 应用启动失败
A: 检查Python版本是否为3.8+，确保所有依赖包已安装

### Q: 无法上传文件
A: 检查文件格式是否支持，文件大小是否过大

### Q: 分析结果不准确
A: 简化版本使用模拟分析，完整版本需要配置AI模型获得真实分析结果

### Q: 浏览器无法打开
A: 手动访问 http://localhost:8501

## 📞 技术支持

如果遇到问题，请：
1. 检查控制台错误信息
2. 确认所有依赖包已正确安装
3. 重启应用尝试解决

## 🔄 版本说明

- **简化版本** (main_simple.py)：功能完整，无需复杂配置，适合快速体验
- **完整版本** (app/main.py)：功能更强大，支持真实AI模型，需要配置API密钥

建议先使用简化版本熟悉功能，再根据需要升级到完整版本。
